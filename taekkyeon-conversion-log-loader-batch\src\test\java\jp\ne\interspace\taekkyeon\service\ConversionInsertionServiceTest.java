/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import com.google.common.base.Joiner;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.params.SetParams;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.CommissionType;
import jp.ne.interspace.taekkyeon.model.ConversionInsertion;
import jp.ne.interspace.taekkyeon.model.ConversionRewards;
import jp.ne.interspace.taekkyeon.model.DeviceType;
import jp.ne.interspace.taekkyeon.model.DuplicationCut;
import jp.ne.interspace.taekkyeon.model.InsertConversionRequest;
import jp.ne.interspace.taekkyeon.model.PublisherData;
import jp.ne.interspace.taekkyeon.model.RewardSettings;
import jp.ne.interspace.taekkyeon.model.RewardType;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ConversionMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.PostbackUrlMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.PublisherAccountMapper;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.model.CommissionType.NET;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link ConversionInsertionService}.
 *
 * <AUTHOR> Shin
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class ConversionInsertionServiceTest {

    private static final String DUPLICATION_CUT_PREFIX = "thailand/dev/duplicationCut/";
    private static final long CAMPAIGN_ID = 10L;
    private static final String TRANSACTION_ID = "2023-01-01-transactionId";
    private static final int RESULT_ID = 3;
    private static final LocalDateTime CONVESION_TIME = LocalDateTime.of(2013, 3, 10, 2,
            5);
    private static final LocalDateTime CONVESION_TIME_HOUR = LocalDateTime.of(2013, 3, 10,
            2, 0);
    private static final String PRODUCT_ID = "productId";
    private static final String UUID = "uuid";
    private static final DeviceType RAW_DEVICE_TYPE = DeviceType.UNKNOWN;
    private static final String RAW_PRODUCT_ID = "productId";
    private static final String RAW_CATEGORY_ID = null;
    private static final RewardType RAW_REWARD_TYPE = RewardType.CPA_FIXED;
    private static final CommissionType RAW_COMMISSION_TYPE = CommissionType.GROSS_AMOUNT_SOLD;
    private static final BigDecimal RAW_REWARD = BigDecimal.valueOf(50);
    private static final BigDecimal RAW_TOTAL_PRICE_REWARD = BigDecimal.valueOf(200);
    private static final BigDecimal RAW_AT_COMMISSION = BigDecimal.valueOf(230);
    private static final BigDecimal RAW_AGENT_COMMISSION = BigDecimal.valueOf(240);
    private static final BigDecimal RAW_PUBLISHER_AGENT_COMMISSION = BigDecimal
            .valueOf(250);
    private static final BigDecimal RAW_REWARD_IN_USD = BigDecimal.valueOf(10);
    private static final BigDecimal RAW_TOTAL_PRICE_REWARD_IN_USD = BigDecimal
            .valueOf(40);
    private static final BigDecimal RAW_AT_COMMISSION_IN_USD = BigDecimal.valueOf(46);
    private static final BigDecimal RAW_AGENT_COMMISSION_IN_USD = BigDecimal.valueOf(48);
    private static final BigDecimal RAW_PUBLISHER_AGENT_COMMISSION_IN_USD = BigDecimal
            .valueOf(50);
    private static final BigDecimal RAW_UNIT_PRICE_IN_USD = BigDecimal.valueOf(30);
    private static final YearMonth TARGET_MONTH = YearMonth.of(2016, 5);
    private static final BigDecimal RAW_DISCOUNT_IN_USD = BigDecimal.valueOf(2);
    private static final LocalDateTime RAW_TIME_HOUR = LocalDateTime.of(2016, 5, 3, 22,
            0);
    private static final long SITE_ID = 9;
    private static final long CREATIVE_ID = 11;
    private static final String MERCHANT_COUNTRY_CODE = "ID";
    private static final int RANK = 5;
    private static final String CUSTOMER_TYPE = "CUSTOMER_TYPE";

    @InjectMocks @Spy
    private ConversionInsertionService underTest;

    @Mock
    private RewardSettingService settingService;

    @Mock
    private RewardCalculatorService calculatorService;

    @Mock
    private PostbackUrlMapper postbackUrlMapper;

    @Mock
    private ConversionMapper conversionMapper;

    @Mock
    private PublisherAccountMapper publisherAccountMapper;

    @Mock
    private MerchantAccountService merchantAccountService;

    @Mock
    private JedisPool jedisPool;

    @Test
    public void testIsDuplicatedDataShouldReturnFalseWhenRedisKeyIsEmpty() {
        // given
        ConversionInsertion conversionInsertion = mock(ConversionInsertion.class);
        String key = "";
        doReturn(key).when(underTest)
                .getDuplicationCutRedisKey(conversionInsertion);

        // when
        boolean actual = underTest.isDuplicatedData(conversionInsertion);

        // then
        assertFalse(actual);
        verifyZeroInteractions(jedisPool);
    }

    @Test
    public void testIsDuplicatedDataShouldReturnTrueWhenRegisteredRedisKey() {
        // given
        ConversionInsertion conversionInsertion = mock(ConversionInsertion.class);
        String key = "suffix";
        doReturn(key).when(underTest)
                .getDuplicationCutRedisKey(conversionInsertion);
        Jedis jedis = mock(Jedis.class);
        when(jedisPool.getResource()).thenReturn(jedis);
        when(jedis.set(eq(DUPLICATION_CUT_PREFIX + key), eq(""),
                any(SetParams.class))).thenReturn(null);

        // when
        boolean actual = underTest.isDuplicatedData(conversionInsertion);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsDuplicatedDataShouldReturnFalseWhenNotRegisteredRedisKey() {
        // given
        ConversionInsertion conversionInsertion = mock(ConversionInsertion.class);
        String key = "suffix";
        doReturn(key).when(underTest)
                .getDuplicationCutRedisKey(conversionInsertion);
        Jedis jedis = mock(Jedis.class);
        when(jedisPool.getResource()).thenReturn(jedis);
        when(jedis.set(eq(DUPLICATION_CUT_PREFIX + key), eq(""),
                any(SetParams.class))).thenReturn("OK");

        // when
        boolean actual = underTest.isDuplicatedData(conversionInsertion);

        // then
        assertFalse(actual);
    }

    @Test
    public void testGetDuplicationCutRedisKeyShouldReturnCorrectKeyWhenOverlapFlagIsFlaseAndVerifyCutFlagIsFalse() {
        // given
        ConversionInsertion conversionInsertion = mock(ConversionInsertion.class);
        DuplicationCut duplicationCut = new DuplicationCut(false, false, 0, 0);
        when(conversionInsertion.getDuplicationCut()).thenReturn(duplicationCut);
        when(conversionInsertion.getCampaignId()).thenReturn(CAMPAIGN_ID);
        when(conversionInsertion.getTransactionId()).thenReturn(TRANSACTION_ID);
        String expected = CAMPAIGN_ID + "_" + TRANSACTION_ID;

        // when
        String actual = underTest.getDuplicationCutRedisKey(conversionInsertion);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetDuplicationCutRedisKeyShouldReturnCorrectKeyWhenOverlapFlagIsFlaseAndVerifyCutFlagIsTrueAndVerifyCutTargetIsNotZeroAndVerifyCutConditionIsOne() {
        // given
        ConversionInsertion conversionInsertion = mock(ConversionInsertion.class);
        DuplicationCut duplicationCut = new DuplicationCut(false, true, 1, 1);
        when(conversionInsertion.getDuplicationCut()).thenReturn(duplicationCut);
        when(conversionInsertion.getCampaignId()).thenReturn(CAMPAIGN_ID);
        when(conversionInsertion.getTransactionId()).thenReturn(TRANSACTION_ID);
        when(conversionInsertion.getConversionTime()).thenReturn(CONVESION_TIME);
        when(conversionInsertion.getProductId()).thenReturn(PRODUCT_ID);
        when(conversionInsertion.getResultId()).thenReturn(RESULT_ID);

        String expected = Joiner.on("_").join(CAMPAIGN_ID, RESULT_ID,
                TRANSACTION_ID.substring(20));

        // when
        String actual = underTest.getDuplicationCutRedisKey(conversionInsertion);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetDuplicationCutRedisKeyShouldReturnCorrectKeyWhenOverlapFlagIsFlaseAndVerifyCutFlagIsTrueAndVerifyCutTargetIsNotZeroAndVerifyCutConditionIsNotOne() {
        // given
        ConversionInsertion conversionInsertion = mock(ConversionInsertion.class);
        DuplicationCut duplicationCut = new DuplicationCut(false, true, 1, 0);
        when(conversionInsertion.getDuplicationCut()).thenReturn(duplicationCut);
        when(conversionInsertion.getCampaignId()).thenReturn(CAMPAIGN_ID);
        when(conversionInsertion.getTransactionId()).thenReturn(TRANSACTION_ID);
        when(conversionInsertion.getConversionTime()).thenReturn(CONVESION_TIME);
        when(conversionInsertion.getProductId()).thenReturn(PRODUCT_ID);
        when(conversionInsertion.getResultId()).thenReturn(RESULT_ID);

        String expected = Joiner.on("_").join(CAMPAIGN_ID, RESULT_ID,
                TRANSACTION_ID.substring(20), PRODUCT_ID);

        // when
        String actual = underTest.getDuplicationCutRedisKey(conversionInsertion);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetDuplicationCutRedisKeyShouldReturnCorrectKeyWhenOverlapFlagIsFlaseAndVerifyCutFlagIsTrueAndVerifyCutTargetIsZeroAndVerifyCutConditionIsNotOne() {
        // given
        ConversionInsertion conversionInsertion = mock(ConversionInsertion.class);
        DuplicationCut duplicationCut = new DuplicationCut(false, true, 0, 0);
        when(conversionInsertion.getDuplicationCut()).thenReturn(duplicationCut);
        when(conversionInsertion.getCampaignId()).thenReturn(CAMPAIGN_ID);
        when(conversionInsertion.getTransactionId()).thenReturn(TRANSACTION_ID);
        when(conversionInsertion.getConversionTime()).thenReturn(CONVESION_TIME);
        when(conversionInsertion.getProductId()).thenReturn(PRODUCT_ID);
        when(conversionInsertion.getResultId()).thenReturn(RESULT_ID);

        String expected = Joiner.on("_").join(CAMPAIGN_ID, RESULT_ID,
                TRANSACTION_ID.substring(20), CONVESION_TIME.toLocalDate(), PRODUCT_ID);

        // when
        String actual = underTest.getDuplicationCutRedisKey(conversionInsertion);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetDuplicationCutRedisKeyShouldReturnCorrectKeyWhenOverlapFlagIsTrue() {
        // given
        ConversionInsertion conversionInsertion = mock(ConversionInsertion.class);
        DuplicationCut duplicationCut = new DuplicationCut(true, false, 0, 0);
        when(conversionInsertion.getDuplicationCut()).thenReturn(duplicationCut);
        when(conversionInsertion.getCampaignId()).thenReturn(CAMPAIGN_ID);
        when(conversionInsertion.getResultId()).thenReturn(RESULT_ID);
        when(conversionInsertion.getUuid()).thenReturn(UUID);
        String expected = Joiner.on("_").join(CAMPAIGN_ID, UUID, RESULT_ID);

        // when
        String actual = underTest.getDuplicationCutRedisKey(conversionInsertion);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetDuplicationCutRedisKeyShouldReturnCorrectKeyWhenOverlapFlagIsTrueAndUuidIsNull() {
        // given
        ConversionInsertion conversionInsertion = mock(ConversionInsertion.class);
        DuplicationCut duplicationCut = new DuplicationCut(true, false, 0, 0);
        when(conversionInsertion.getDuplicationCut()).thenReturn(duplicationCut);
        when(conversionInsertion.getCampaignId()).thenReturn(CAMPAIGN_ID);
        when(conversionInsertion.getResultId()).thenReturn(RESULT_ID);
        when(conversionInsertion.getUuid()).thenReturn(null);

        // when
        String actual = underTest.getDuplicationCutRedisKey(conversionInsertion);

        // then
        assertEquals(EMPTY, actual);
    }

    @Test
    public void testCalculateShouldReturnCorrectDataWhenCalled() {
        // given
        RewardSettings settings = mock(RewardSettings.class);
        InsertConversionRequest request = new InsertConversionRequest(RAW_TIME_HOUR,
                CREATIVE_ID, SITE_ID, CAMPAIGN_ID, RAW_DEVICE_TYPE, null,
                DUPLICATION_CUT_PREFIX, DUPLICATION_CUT_PREFIX, DUPLICATION_CUT_PREFIX,
                RAW_TIME_HOUR, RAW_TIME_HOUR, TRANSACTION_ID, TRANSACTION_ID,
                DUPLICATION_CUT_PREFIX, RESULT_ID, DUPLICATION_CUT_PREFIX, RESULT_ID,
                RAW_AGENT_COMMISSION_IN_USD, DUPLICATION_CUT_PREFIX, RAW_AGENT_COMMISSION,
                DUPLICATION_CUT_PREFIX, PRODUCT_ID, UUID, RAW_CATEGORY_ID,
                RAW_DISCOUNT_IN_USD, UUID, TRANSACTION_ID, RAW_PRODUCT_ID,
                RAW_CATEGORY_ID, PRODUCT_ID, DUPLICATION_CUT_PREFIX);
        ConversionInsertion conversion = new ConversionInsertion(request,
                mock(PublisherData.class), "", mock(DuplicationCut.class));
        Set<String> transactionIds = Collections.emptySet();
        ConversionRewards conversionRewards = new ConversionRewards(RAW_REWARD,
                RAW_TOTAL_PRICE_REWARD, RAW_AT_COMMISSION, RAW_AGENT_COMMISSION,
                RAW_PUBLISHER_AGENT_COMMISSION, RAW_REWARD_IN_USD,
                RAW_TOTAL_PRICE_REWARD_IN_USD, RAW_AT_COMMISSION_IN_USD,
                RAW_AGENT_COMMISSION_IN_USD, RAW_PUBLISHER_AGENT_COMMISSION_IN_USD,
                RAW_DISCOUNT_IN_USD, RAW_UNIT_PRICE_IN_USD);
        when(calculatorService.calculateConversionRewardsBy(conversion, settings,
                transactionIds, TARGET_MONTH)).thenReturn(conversionRewards);
        when(calculatorService.getRewardType(settings, RAW_REWARD_TYPE))
                .thenReturn(RAW_REWARD_TYPE);
        when(calculatorService.getCommissionType(settings, NET))
                .thenReturn(RAW_COMMISSION_TYPE);

        // when
        ConversionInsertion actual = underTest.calculate(conversion, settings);

        // then
        assertNotNull(actual);
        assertEquals(RAW_AT_COMMISSION, actual.getAtCommission());
        assertEquals(RAW_AGENT_COMMISSION, actual.getAgentCommission());
        assertEquals(RAW_PUBLISHER_AGENT_COMMISSION,
                actual.getPublisherAgentCommission());
        assertEquals(RAW_REWARD_TYPE, actual.getRewardType());
        assertEquals(RAW_COMMISSION_TYPE, actual.getCommissionType());
        assertEquals(RAW_REWARD, actual.getReward());
        assertEquals(RAW_TOTAL_PRICE_REWARD, actual.getTransactionAmountReward());
        assertEquals(RAW_UNIT_PRICE_IN_USD, actual.getUnitPriceInUsd());
    }

    @Test
    public void testConvertShouldReturnCorrectDataWhenCalled() throws Exception {
        // given
        InsertConversionRequest request1 = new InsertConversionRequest(null, 0, SITE_ID,
                CAMPAIGN_ID, RAW_DEVICE_TYPE, null, null, null, null, null,
                CONVESION_TIME, null, null, null, RANK, null, RESULT_ID, null, null, null,
                null, PRODUCT_ID, null, RAW_CATEGORY_ID, null, null, null, null,
                CUSTOMER_TYPE, null, null);
        InsertConversionRequest request2 = new InsertConversionRequest(null, 0, SITE_ID,
                CAMPAIGN_ID, RAW_DEVICE_TYPE, null, null, null, null, null,
                CONVESION_TIME, null, null, null, RANK, null, RESULT_ID, null, null, null,
                null, PRODUCT_ID, null, RAW_CATEGORY_ID, null, null, null, null,
                CUSTOMER_TYPE, null, null);
        when(merchantAccountService.findCountryCodeBy(CAMPAIGN_ID))
                .thenReturn(MERCHANT_COUNTRY_CODE);
        PublisherData publisherData = mock(PublisherData.class);
        when(publisherAccountMapper.findPublisherDataBy(SITE_ID))
                .thenReturn(publisherData);
        DuplicationCut duplicationCut = mock(DuplicationCut.class);
        when(conversionMapper.findDuplicationCutBy(CAMPAIGN_ID, SITE_ID))
                .thenReturn(duplicationCut);
        ConversionInsertion conversionInsertion1 = mock(ConversionInsertion.class);
        ConversionInsertion conversionInsertion2 = mock(ConversionInsertion.class);
        doReturn(conversionInsertion1).when(underTest).createConversionInsertion(request1,
                MERCHANT_COUNTRY_CODE, publisherData, duplicationCut);
        doReturn(conversionInsertion2).when(underTest).createConversionInsertion(request2,
                MERCHANT_COUNTRY_CODE, publisherData, duplicationCut);
        doReturn(false).when(underTest).isDuplicatedData(conversionInsertion1);
        doReturn(true).when(underTest).isDuplicatedData(conversionInsertion2);
        RewardSettings settings = mock(RewardSettings.class);
        doReturn(settings).when(settingService).findRewardSettings(CAMPAIGN_ID,
                CONVESION_TIME_HOUR, RANK, RAW_DEVICE_TYPE, RESULT_ID, PRODUCT_ID,
                RAW_CATEGORY_ID, CUSTOMER_TYPE);
        doReturn(conversionInsertion1).when(underTest).calculate(conversionInsertion1,
                settings);

        // when
        List<ConversionInsertion> actual = underTest
                .convert(Arrays.asList(request1, request2));

        // then
        assertEquals(1, actual.size());
        assertSame(conversionInsertion1, actual.get(0));
    }
}
