/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.processor;

import javax.inject.Singleton;

import com.google.inject.Inject;

import lombok.extern.slf4j.Slf4j;

import org.easybatch.core.processor.RecordProcessor;

import jp.ne.interspace.taekkyeon.factory.LogItemFactory;
import jp.ne.interspace.taekkyeon.model.LogItem;
import jp.ne.interspace.taekkyeon.model.LogRecord;
import jp.ne.interspace.taekkyeon.model.LogRecordPayload;
import jp.ne.interspace.taekkyeon.model.SqsRecord;
import jp.ne.interspace.taekkyeon.model.SqsRecordPayload;

/**
 * {@link RecordProcessor} implementation for processing log items.
 *
 * <AUTHOR>
 */
@Slf4j
@Singleton
public class LogRecordProcessor
        implements RecordProcessor<SqsRecord, LogRecord> {

    @Inject
    private LogItemFactory logItemFactory;

    @Override
    public LogRecord processRecord(SqsRecord record) throws Exception {
        return parseLogItemIn(record);
    }

    private LogRecord parseLogItemIn(SqsRecord record) {
        SqsRecordPayload payload = record.getPayload();
        String rawLogItem = payload.getMessage();
        try {
            LogItem parsedLogItem = logItemFactory.createFrom(rawLogItem);
            return new LogRecord(record.getHeader(),
                    new LogRecordPayload(payload.getReceiptHandle(), parsedLogItem));
        } catch (Exception ex) {
            log.warn("Failed to parse log item: " + rawLogItem, ex);
            return new LogRecord(record.getHeader(),
                    new LogRecordPayload(payload.getReceiptHandle(), null));
        }
    }
}
