/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * DTO for holding to find postback from cache.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter @EqualsAndHashCode
public class PostbackKey {

    private final long siteId;
    private final long creativeId;
}
