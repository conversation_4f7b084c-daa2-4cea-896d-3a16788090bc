/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import com.google.common.collect.ImmutableMap;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.common.StringHelper;
import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.DeviceOs;
import jp.ne.interspace.taekkyeon.model.DeviceType;
import jp.ne.interspace.taekkyeon.validator.LogValidator;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link LogItemParserService}.
 *
 * <AUTHOR> Varga
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class LogItemParserServiceTest {

    private static final String REQUEST_TIME = "REQUEST_TIME";
    private static final String BANNER_ID = "BANNER_ID";
    private static final String PARTNER_SITE_NO = "PARTNER_SITE_NO";
    private static final String MERCHANT_CAMPAIGN_NO = "MERCHANT_CAMPAIGN_NO";
    private static final String DEVICE_TYPE = "DEVICE_TYPE";
    private static final String IP_ADDRESS = "IP_ADDRESS";
    private static final String REFERER = "REFERER";
    private static final String USER_AGENT = "USER-AGENT";
    private static final String TRACKING_ID = "TRACKING_ID";
    private static final String PRODUCT_ID = "GOODS_ID";
    private static final String QUANTITY = "QUANTITY";
    private static final String UNIT_PRICE = "UNIT_PRICE";
    private static final String CATEGORY_ID = "CATEGORY_ID";
    private static final String POINTBACK_ID = "PBID";
    private static final String UUID = "UUID";
    private static final String DISCOUNT = "DISCOUNT";
    private static final String ORIGINAL_TOTAL_PRICE = "ORIGINAL_TOTAL_PRICE";
    private static final String CLICK_REFERER = "CLICK_REFERER";
    private static final String CLICK_URL = "CLICK_URL";
    private static final String CLICK_USER_AGENT = "CLICK_USER_AGENT";
    private static final String IDENTIFIER = "IDENTIFIER";
    private static final String RESULT_ID = "RESULT_ID";
    private static final String SESSION_ID = "SESSION_ID";
    private static final String REQUEST = "REQUEST";
    private static final String CUSTOMER_TYPE = "CUSTOMER_TYPE";
    private static final String LANGUAGE = "LANGUAGE";
    private static final String CLICK_IP_ADDRESS = "CLICK_IP_ADDRESS";
    private static final String CURRENCY = "CURRENCY";
    private static final String CURRENCY_USD = "USD";

    private static final String RAW_LOG_ITEM = "Request_Time=requestTimeValue#Banner_Id#CLICK_REFERER=stringValue1Key=Value1&stringValue2Key=Value2";

    private static final String VALID_RAW_ID = "1";
    private static final long VALID_ID = 1;
    private static final String INVALID_RAW_ID = "notANumber";
    private static final long DEFAULT_QUANTITY_VALUE = 1;

    private static final String STRING_VALUE = "stringValue";
    private static final String TRUNCATED_STRING_VALUE = "truncatedStringValue";
    private static final String NUMBER_VALUE = "1";
    private static final String ENCODED_URL = "https%3A%2F%2Fgoogle.com%3Ftest1%3D123%26test2%3D%23asdf";
    private static final String DECODED_URL = "https://google.com?test1=123&test2=#asdf";

    private static final int PRODUCT_ID_MAX_BYTE_LENGTH = 256;
    private static final int CATEGORY_ID_MAX_BYTE_LENGTH = 256;
    private static final int IP_ADDRESS_MAX_BYTE_LENGTH = 256;
    private static final int REFERER_MAX_BYTE_LENGTH = 2048;
    private static final int USER_AGENT_MAX_BYTE_LENGTH = 512;
    private static final int SESSION_ID_MAX_BYTE_LENGTH = 256;
    private static final int POINTBACK_ID_MAX_BYTE_LENGTH = 64;
    private static final int IDENTIFIER_MAX_BYTE_LENGTH = 256;
    private static final int CONVERSION_PARAMETER_MAX_BYTE_LENGTH = 2048;
    private static final int PARAMETER_NAME_MAX_BYTE_LENGTH = 128;
    private static final int CLICK_PARAMETER_MAX_BYTE_LENGTH = 2048;
    private static final int TRACKING_ID_MAX_LENGTH_VALUE = 40;
    private static final int CUSTOMER_TYPE_MAX_BYTE_LENGTH = 64;

    @Spy
    @InjectMocks
    private LogItemParserService underTest;

    @Mock
    private StringHelper stringHelper;

    @Mock
    private LogValidator logValidator;

    @Test
    public void testParseShouldReturnAllFieldsCorrectlyWhenRawLogItemIsNotEmpty() {
        // when
        Map<String, String> actual = underTest.parse(RAW_LOG_ITEM);

        // then
        assertNotNull(actual);
        assertEquals(3, actual.size());
        assertEquals("requestTimeValue", actual.get(REQUEST_TIME));
        assertTrue(actual.containsKey(BANNER_ID));
        assertEquals(null, actual.get(BANNER_ID));
        assertTrue(actual.containsKey(CLICK_REFERER));
        assertEquals("stringValue1Key=Value1&stringValue2Key=Value2",
                actual.get(CLICK_REFERER));
    }

    @Test
    public void testGetCreativeIdFromShouldReturnCorrectCreativeIdWhenRawIdIsANumber() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(BANNER_ID, VALID_RAW_ID);

        // when
        long actual = underTest.getCreativeIdFrom(parsedLogItem);

        // then
        assertNotNull(actual);
        assertEquals(VALID_ID, actual);
    }

    @Test(expected = NumberFormatException.class)
    public void testGetCreativeIdFromShouldThrowExceptionWhenRawIdIsNotANumber() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(BANNER_ID, INVALID_RAW_ID);

        // when
        underTest.getCreativeIdFrom(parsedLogItem);
    }

    @Test
    public void testGetSiteIdFromShouldReturnCorrectSiteIdWhenRawIdIsANumber() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(PARTNER_SITE_NO, VALID_RAW_ID);

        // when
        long actual = underTest.getSiteIdFrom(parsedLogItem);

        // then
        assertNotNull(actual);
        assertEquals(VALID_ID, actual);
    }

    @Test(expected = NumberFormatException.class)
    public void testGetSiteIdFromShouldThrowExceptionWhenRawIdIsNotANumber() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(PARTNER_SITE_NO, INVALID_RAW_ID);

        // when
        underTest.getSiteIdFrom(parsedLogItem);
    }

    @Test
    public void testGetCampaignIdFromShouldReturnCorrectCampaignIdWhenRawIdIsANumber() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(MERCHANT_CAMPAIGN_NO, VALID_RAW_ID);

        // when
        long actual = underTest.getCampaignIdFrom(parsedLogItem);

        // then
        assertNotNull(actual);
        assertEquals(VALID_ID, actual);
    }

    @Test(expected = NumberFormatException.class)
    public void testGetCampaignIdFromShouldThrowExceptionWhenRawIdIsNotANumber() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(MERCHANT_CAMPAIGN_NO, INVALID_RAW_ID);

        // when
        underTest.getCampaignIdFrom(parsedLogItem);
    }

    @Test
    public void testGetDeviceTypeFromShouldReturnCorrectDeviceTypeWhenRawDeviceTypeIsANumber() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(DEVICE_TYPE, "1");

        // when
        DeviceType actual = underTest.getDeviceTypeFrom(parsedLogItem);

        // then
        assertNotNull(actual);
        assertEquals(DeviceType.DESKTOP, actual);
    }

    @Test
    public void testGetDeviceTypeFromShouldReturnCorrectDeviceTypeWhenRawDeviceTypeIsAnInvalidDeviceType() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(DEVICE_TYPE, "-1");

        // when
        DeviceType actual = underTest.getDeviceTypeFrom(parsedLogItem);

        // then
        assertNotNull(actual);
        assertEquals(DeviceType.UNKNOWN, actual);
    }

    @Test
    public void testGetDeviceTypeFromShouldReturnUnknownDeviceTypeWhenRawDeviceTypeIsNotAvailable() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();

        // when
        DeviceType actual = underTest.getDeviceTypeFrom(parsedLogItem);

        // then
        assertNotNull(actual);
        assertEquals(DeviceType.UNKNOWN, actual);
    }

    @Test(expected = NumberFormatException.class)
    public void testGetDeviceTypeFromShouldThrowExceptionWhenRawDeviceTypeIsNotANumber() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(DEVICE_TYPE, "notANumber");

        // when
        underTest.getDeviceTypeFrom(parsedLogItem);
    }

    @Test
    public void testGetIpAddressFromShouldReturnTruncatedIpAddressWhenCalled() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(IP_ADDRESS, STRING_VALUE);

        when(stringHelper.truncateToBytes(STRING_VALUE, IP_ADDRESS_MAX_BYTE_LENGTH))
                .thenReturn(TRUNCATED_STRING_VALUE);

        // when
        String actual = underTest.getIpAddressFrom(parsedLogItem);

        // then
        assertEquals(TRUNCATED_STRING_VALUE, actual);
    }

    @Test
    public void testGetRefererFromShouldReturnTruncatedRefererWhenCalled() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(REFERER, STRING_VALUE);

        when(stringHelper.truncateToBytes(STRING_VALUE, REFERER_MAX_BYTE_LENGTH))
                .thenReturn(TRUNCATED_STRING_VALUE);

        // when
        String actual = underTest.getRefererFrom(parsedLogItem);

        // then
        assertEquals(TRUNCATED_STRING_VALUE, actual);
    }

    @Test
    public void testGetUserAgentFromShouldReturnTruncatedUserAgentWhenCalled() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(USER_AGENT, STRING_VALUE);

        when(stringHelper.truncateToBytes(STRING_VALUE, USER_AGENT_MAX_BYTE_LENGTH))
                .thenReturn(TRUNCATED_STRING_VALUE);

        // when
        String actual = underTest.getUserAgentFrom(parsedLogItem);

        // then
        assertEquals(TRUNCATED_STRING_VALUE, actual);
    }

    @Test
    public void testGetTrackingIdFromShouldReturnCorrectTrackingIdWhenCalled() {
        // given
        Map<String, String> parsedLogItem = ImmutableMap.of(TRACKING_ID, STRING_VALUE);
        when(stringHelper.truncateToBytes(STRING_VALUE, TRACKING_ID_MAX_LENGTH_VALUE))
                .thenReturn(TRUNCATED_STRING_VALUE);

        // when
        String actual = underTest.getTrackingIdFrom(parsedLogItem);

        // then
        assertEquals(TRUNCATED_STRING_VALUE, actual);
    }

    @Test
    public void testGetProductIdFromShouldReturnTruncatedProductIdWhenCalled() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(PRODUCT_ID, STRING_VALUE);
        when(stringHelper.truncateToBytes(STRING_VALUE, PRODUCT_ID_MAX_BYTE_LENGTH))
                .thenReturn(TRUNCATED_STRING_VALUE);

        // when
        String actual = underTest.getProductIdFrom(parsedLogItem);

        // then
        assertEquals(TRUNCATED_STRING_VALUE, actual);
    }

    @Test
    public void testGetDeviceOsFromShouldReturnUnknownWhenGivenIncorrectData() {
        // when
        DeviceOs actual = underTest.getDeviceOsFrom(null);

        // then
        assertEquals(DeviceOs.UNKNOWN, actual);
    }

    @Test
    public void testGetDeviceOsFromShouldReturnWindowsWhenGivenCorrectData() {
        // given
        String userAgent = "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 "
                + "(KHTML, like Gecko) Chrome/63.0.3239.84 Safari/537.36";

        // when
        DeviceOs actual = underTest.getDeviceOsFrom(userAgent);

        // then
        assertEquals(DeviceOs.WINDOWS, actual);
    }

    @Test
    public void testGetQuantityFromShouldReturnDefaultValueWhenQuantityDoesNotExistAndConversionIsNotProductOrCategoryReward() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        int resultId = 1;

        // when
        long actual = underTest.getQuantityFrom(parsedLogItem, resultId);

        // then
        assertEquals(1, actual);
    }

    @Test(expected = NumberFormatException.class)
    public void testGetQuantityFromShouldThrowExceptionWhenQuantityDoesNotExistAndConversionIsProductReward() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        int resultId = 3;

        // when
        underTest.getQuantityFrom(parsedLogItem, resultId);
    }

    @Test(expected = NumberFormatException.class)
    public void testGetQuantityFromShouldThrowExceptionWhenQuantityDoesNotExistAndConversionIsCategoryReward() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        int resultId = 30;

        // when
        underTest.getQuantityFrom(parsedLogItem, resultId);
    }

    @Test(expected = NumberFormatException.class)
    public void testGetQuantityFromShouldThrowExceptionWhenQuantityIsNotNumericAndConversionIsCategoryReward() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(QUANTITY, "a1");
        int resultId = 30;

        // when
        underTest.getQuantityFrom(parsedLogItem, resultId);
    }

    @Test
    public void testGetQuantityFromShouldReturnTheDefaultValueWhenQuantityIsNotNumericAndConversionIsNotProductOrCategoryReward() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(QUANTITY, "a1");
        int resultId = 2;

        // when
        long actual = underTest.getQuantityFrom(parsedLogItem, resultId);

        // then
        assertEquals(DEFAULT_QUANTITY_VALUE, actual);
    }

    @Test
    public void testGetQuantityFromShouldReturnTheDefaultValueWhenQuantityValueIsLessThanDefaultValueAndConversionIsNotProductOrCategoryReward() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(QUANTITY, "-1");
        int resultId = 2;

        // when
        long actual = underTest.getQuantityFrom(parsedLogItem, resultId);

        // then
        assertEquals(DEFAULT_QUANTITY_VALUE, actual);
    }

    @Test
    public void testGetQuantityFromShouldReturnTheDefaultValueWhenQuantityValueEqualsToDefaultValueAndConversionIsCategoryReward() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(QUANTITY, "1");
        long expected = 1;
        int resultId = 30;

        // when
        long actual = underTest.getQuantityFrom(parsedLogItem, resultId);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetQuantityFromShouldReturnTheDefaultValueWhenQuantityValueEqualsToDefaultValueAndConversionIsProductReward() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(QUANTITY, "1");
        long expected = 1;
        int resultId = 3;

        // when
        long actual = underTest.getQuantityFrom(parsedLogItem, resultId);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetQuantityFromShouldReturnCorrectValueWhenQuantityValueIsBiggerThanDefaultValueAndConversionIsCategoryReward() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(QUANTITY, "2");
        long expected = 2;
        int resultId = 30;

        // when
        long actual = underTest.getQuantityFrom(parsedLogItem, resultId);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetQuantityFromShouldReturnCorrectValueWhenQuantityValueIsBiggerThanDefaultValueAndConversionIsProductReward() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(QUANTITY, "2");
        long expected = 2;
        int resultId = 3;

        // when
        long actual = underTest.getQuantityFrom(parsedLogItem, resultId);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetQuantityFromShouldReturnCorrectValueWhenQuantityValueIsBiggerThanDefaultValueAndConversionIsNotCategoryOrProductReward() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(QUANTITY, "2");
        long expected = 2;
        int resultId = 2;

        // when
        long actual = underTest.getQuantityFrom(parsedLogItem, resultId);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetQuantityFromShouldReturnTheDefaultValueWhenQuantityValueEqualsToDefaultValueAndAndConversionIsNotIsNotProductOrCategoryReward() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(QUANTITY, "1");
        long expected = 1;
        int resultId = 2;

        // when
        long actual = underTest.getQuantityFrom(parsedLogItem, resultId);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetQuantityFromShouldReturnTheDefaultValueWhenQuantityDoesNotExistAndConversionIsNotProductOrCategoryReward() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        int resultId = 2;

        // when
        long actual = underTest.getQuantityFrom(parsedLogItem, resultId);

        // then
        assertEquals(DEFAULT_QUANTITY_VALUE, actual);
    }

    @Test
    public void testGetQuantityFromShouldThrowExceptionWhenQuantityValueIsLessThanDefaultValueAndConversionIsProductReward() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(QUANTITY, "0");
        int resultId = 3;

        // when
        try {
            underTest.getQuantityFrom(parsedLogItem, resultId);
            fail();
        } catch (TaekkyeonException ex) {

            // then
            assertEquals("The parameter [QUANTITY] is invalid", ex.getMessage());
        }
    }

    @Test
    public void testGetQuantityFromShouldThrowExceptionWhenQuantityValueIsLessThanDefaultValueAndConversionIsCategoryReward() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(QUANTITY, "0");
        int resultId = 30;

        // when
        try {
            underTest.getQuantityFrom(parsedLogItem, resultId);
            fail();
        } catch (TaekkyeonException ex) {

            // then
            assertEquals("The parameter [QUANTITY] is invalid", ex.getMessage());
        }
    }

    @Test
    public void testGetUnitPriceFromShouldReturnUnitPriceWhenGivenValidUnitPrice() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(UNIT_PRICE, NUMBER_VALUE);
        int resultId = 1;

        // when
        BigDecimal actual = underTest.getUnitPriceFrom(parsedLogItem, resultId);

        // then
        assertEquals(1, actual.intValue());
    }

    @Test
    public void testGetUnitPriceFromShouldReturnZeroWhenUnitPriceDoesNotExistAndConversionIsNotProductOrCategoryReward() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        int resultId = 1;

        // when
        BigDecimal actual = underTest.getUnitPriceFrom(parsedLogItem, resultId);

        // then
        assertEquals(0, actual.intValue());
    }

    @Test(expected = NullPointerException.class)
    public void testGetUnitPriceFromShouldThrowExceptionWhenUnitPriceDoesNotExistAndConversionIsProductReward() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        int resultId = 3;

        // when
        underTest.getUnitPriceFrom(parsedLogItem, resultId);
    }

    @Test(expected = NullPointerException.class)
    public void testGetUnitPriceFromShouldThrowExceptionWhenUnitPriceDoesNotExistAndConversionIsCategoryReward() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        int resultId = 30;

        // when
        underTest.getUnitPriceFrom(parsedLogItem, resultId);
    }

    @Test
    public void testGetUnitPriceFromShouldReturnCorrectUnitPriceWhenUnitPriceExistsAndConversionIsCategoryReward() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(UNIT_PRICE, "123");
        int resultId = 30;
        BigDecimal expected = BigDecimal.valueOf(123);

        // when
        BigDecimal actual = underTest.getUnitPriceFrom(parsedLogItem, resultId);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetUnitPriceFromShouldReturnCorrectUnitPriceWhenUnitPriceExistsAndConversionIsProductReward() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(UNIT_PRICE, "123");
        int resultId = 3;
        BigDecimal expected = BigDecimal.valueOf(123);

        // when
        BigDecimal actual = underTest.getUnitPriceFrom(parsedLogItem, resultId);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetCategoryIdFromShouldReturnTruncatedCategoryIdWhenGivenValidCategoryValue() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(CATEGORY_ID, STRING_VALUE);
        int resultId = 2;
        when(stringHelper.truncateToBytes(STRING_VALUE, CATEGORY_ID_MAX_BYTE_LENGTH))
                .thenReturn(TRUNCATED_STRING_VALUE);

        // when
        String actual = underTest.getCategoryIdFrom(parsedLogItem, resultId);

        // then
        assertEquals(TRUNCATED_STRING_VALUE, actual);
    }

    @Test
    public void testGetCategoryIdFromShouldReturnNullWhenCategoryIdDoesNotExistAndConversionIsNotCategoryReward() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        int resultId = 3;

        // when
        String actual = underTest.getCategoryIdFrom(parsedLogItem, resultId);

        // then
        assertNull(actual);
    }

    @Test
    public void testGetCategoryIdFromShouldThrowExceptionWhenCategoryIdDoesNotExistAndConversionIsCategoryReward() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        int resultId = 30;

        // when
        try {
            underTest.getCategoryIdFrom(parsedLogItem, resultId);
            fail();
        } catch (TaekkyeonException ex) {

            // then
            assertEquals("The parameter [CATEGORY_ID] is invalid", ex.getMessage());
        }
    }

    @Test
    public void testGetPointbackIdFromShouldReturnNullWhenPostbackIdIsNull() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(POINTBACK_ID, null);

        // when
        String actual = underTest.getPointbackIdFrom(parsedLogItem);

        // then
        assertNull(actual);
    }

    @Test
    public void testGetPointbackIdFromShouldReturnEmptyWhenPostbackIdIsEmpty() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(POINTBACK_ID, "");

        // when
        String actual = underTest.getPointbackIdFrom(parsedLogItem);

        // then
        assertEquals("", actual);
    }

    @Test
    public void testGetPointbackIdFromShouldReturnPostbackIdCorrectlyIdWhenGivenValidPostbackId() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(POINTBACK_ID, STRING_VALUE);

        // when
        String actual = underTest.getPointbackIdFrom(parsedLogItem);

        // then
        assertEquals(STRING_VALUE, actual);

        verify(logValidator).validateMaxByteCountOf(STRING_VALUE,
                POINTBACK_ID_MAX_BYTE_LENGTH, POINTBACK_ID);
    }

    @Test
    public void testGetUuidFromShouldReturnUuidWhenCalled() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(UUID, STRING_VALUE);

        // when
        String actual = underTest.getUuidFrom(parsedLogItem);

        // then
        assertEquals(STRING_VALUE, actual);
    }

    @Test
    public void testGetDiscountFromShouldReturnDiscountWhenCalled() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(DISCOUNT, NUMBER_VALUE);

        // when
        BigDecimal actual = underTest.getDiscountFrom(parsedLogItem);

        // then
        assertEquals(1, actual.intValue());
    }

    @Test
    public void testGetDiscountFromShouldReturnZeroWhenGivenEmptyData() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();

        // when
        BigDecimal actual = underTest.getDiscountFrom(parsedLogItem);

        // then
        assertEquals(0, actual.intValue());
    }

    @Test
    public void testGetOriginalTotalPriceFromShouldReturnOriginalTotalPriceWhenCalled() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(ORIGINAL_TOTAL_PRICE, NUMBER_VALUE);

        // when
        BigDecimal actual = underTest.getOriginalTotalPriceFrom(parsedLogItem);

        // then
        assertEquals(1, actual.intValue());
    }

    @Test
    public void testGetOriginalTotalPriceFromShouldReturnZeroWhenGivenEmptyData() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();

        // when
        BigDecimal actual = underTest.getOriginalTotalPriceFrom(parsedLogItem);

        // then
        assertEquals(0, actual.intValue());
    }

    @Test
    public void testGetClickRefererFromShouldReturnClickRefererWhenCalled() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(CLICK_REFERER, STRING_VALUE);
        when(stringHelper.truncateToBytes(STRING_VALUE, REFERER_MAX_BYTE_LENGTH))
                .thenReturn(TRUNCATED_STRING_VALUE);
        doReturn(TRUNCATED_STRING_VALUE).when(underTest).decodeUrl(
                TRUNCATED_STRING_VALUE);

        // when
        String actual = underTest.getClickRefererFrom(parsedLogItem);

        // then
        assertEquals(TRUNCATED_STRING_VALUE, actual);
    }

    @Test
    public void testGetClickUrlFromShouldReturnClickUrlWhenCalled() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(CLICK_URL, STRING_VALUE);
        when(stringHelper.truncateToBytes(STRING_VALUE,
                CONVERSION_PARAMETER_MAX_BYTE_LENGTH)).thenReturn(STRING_VALUE);
        doReturn(STRING_VALUE).when(underTest).decodeUrl(STRING_VALUE);

        // when
        String actual = underTest.getClickUrlFrom(parsedLogItem);

        // then
        assertEquals(STRING_VALUE, actual);
    }

    @Test
    public void testGetClickUserAgentFromShouldReturnClickUserAgentWhenCalled() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(CLICK_USER_AGENT, STRING_VALUE);

        // when
        String actual = underTest.getClickUserAgentFrom(parsedLogItem);

        // then
        assertEquals(STRING_VALUE, actual);
    }

    @Test
    public void testGetConversionParametersFromShouldReturnConversionParametersWhenCalled() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(RESULT_ID, NUMBER_VALUE);
        parsedLogItem.put("CV_PARAM_KEY", STRING_VALUE);
        String parameterKey = "key";

        when(stringHelper.truncateToBytes(parameterKey,
                PARAMETER_NAME_MAX_BYTE_LENGTH)).thenReturn(parameterKey);
        when(stringHelper.truncateToBytes(STRING_VALUE,
                CONVERSION_PARAMETER_MAX_BYTE_LENGTH)).thenReturn(TRUNCATED_STRING_VALUE);

        // when
        Map<String, String> actual = underTest.getConversionParametersFrom(parsedLogItem);

        // then
        assertEquals(1, actual.size());
        assertTrue(actual.containsKey("key"));
        assertEquals(TRUNCATED_STRING_VALUE, actual.get("key"));
    }

    @Test
    public void testGetConversionParametersFromShouldReturnEmptyDataWhenGivenNonCvParam() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(RESULT_ID, NUMBER_VALUE);
        parsedLogItem.put(CURRENCY, CURRENCY_USD);

        when(stringHelper.truncateToBytes(STRING_VALUE,
                CONVERSION_PARAMETER_MAX_BYTE_LENGTH)).thenReturn(TRUNCATED_STRING_VALUE);

        // when
        Map<String, String> actual = underTest.getConversionParametersFrom(parsedLogItem);

        // then
        assertEquals(0, actual.size());
    }

    @Test
    public void testGetClickParametersFromShouldReturnClickParametersWhenClickParametersAreAvailable() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(RESULT_ID, NUMBER_VALUE);
        parsedLogItem.put("CV_PARAM_TEST", "test");
        parsedLogItem.put("KEY1", null);
        parsedLogItem.put("KEY2", STRING_VALUE);
        parsedLogItem.put("KEY3", "");
        parsedLogItem.put("KEY4", "NULL");
        parsedLogItem.put(POINTBACK_ID, STRING_VALUE);
        parsedLogItem.put(CLICK_URL, ENCODED_URL);
        parsedLogItem.put(CLICK_REFERER, ENCODED_URL);
        parsedLogItem.put(CLICK_USER_AGENT, STRING_VALUE);

        String lowerCaseParameterName1 = "key2";
        String lowerCaseParameterName2 = "pbid";
        String lowerCaseParameterName3 = "click_url";
        String lowerCaseParameterName4 = "click_referer";
        String lowerCaseParameterName5 = "click_user_agent";

        when(stringHelper.truncateToBytes(lowerCaseParameterName1,
                PARAMETER_NAME_MAX_BYTE_LENGTH)).thenReturn(lowerCaseParameterName1);
        when(stringHelper.truncateToBytes(lowerCaseParameterName2,
                PARAMETER_NAME_MAX_BYTE_LENGTH)).thenReturn(lowerCaseParameterName2);
        when(stringHelper.truncateToBytes(lowerCaseParameterName3,
                PARAMETER_NAME_MAX_BYTE_LENGTH)).thenReturn(lowerCaseParameterName3);
        when(stringHelper.truncateToBytes(lowerCaseParameterName4,
                PARAMETER_NAME_MAX_BYTE_LENGTH)).thenReturn(lowerCaseParameterName4);
        when(stringHelper.truncateToBytes(lowerCaseParameterName5,
                PARAMETER_NAME_MAX_BYTE_LENGTH)).thenReturn(lowerCaseParameterName5);
        when(stringHelper.truncateToBytes(STRING_VALUE, CLICK_PARAMETER_MAX_BYTE_LENGTH))
                .thenReturn(TRUNCATED_STRING_VALUE);
        when(stringHelper.truncateToBytes(ENCODED_URL, CLICK_PARAMETER_MAX_BYTE_LENGTH))
                .thenReturn(ENCODED_URL);

        // when
        Map<String, String> actual = underTest.getClickParametersFrom(parsedLogItem);

        // then
        assertEquals(5, actual.size());
        assertEquals(TRUNCATED_STRING_VALUE, actual.get(lowerCaseParameterName1));
        assertEquals(TRUNCATED_STRING_VALUE, actual.get(lowerCaseParameterName2));
        assertEquals(DECODED_URL, actual.get(lowerCaseParameterName3));
        assertEquals(DECODED_URL, actual.get(lowerCaseParameterName4));
        assertEquals(TRUNCATED_STRING_VALUE, actual.get(lowerCaseParameterName5));
    }

    @Test
    public void testGetClickParametersFromShouldReturnClickParametersWhenClickParametersAreAvailableAndAtnctDecodeUrlIsTrue() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(RESULT_ID, NUMBER_VALUE);
        parsedLogItem.put("CV_PARAM_TEST", "test");
        parsedLogItem.put("KEY1", null);
        parsedLogItem.put("KEY2", STRING_VALUE);
        parsedLogItem.put("KEY3", "");
        parsedLogItem.put("KEY4", "NULL");
        parsedLogItem.put(POINTBACK_ID, STRING_VALUE);
        parsedLogItem.put(CLICK_URL, ENCODED_URL);
        parsedLogItem.put(CLICK_REFERER, ENCODED_URL);
        parsedLogItem.put(CLICK_USER_AGENT, STRING_VALUE);

        String lowerCaseParameterName1 = "key2";
        String lowerCaseParameterName2 = "pbid";
        String lowerCaseParameterName3 = "click_url";
        String lowerCaseParameterName4 = "click_referer";
        String lowerCaseParameterName5 = "click_user_agent";

        when(stringHelper.truncateToBytes(lowerCaseParameterName1,
                PARAMETER_NAME_MAX_BYTE_LENGTH)).thenReturn(lowerCaseParameterName1);
        when(stringHelper.truncateToBytes(lowerCaseParameterName2,
                PARAMETER_NAME_MAX_BYTE_LENGTH)).thenReturn(lowerCaseParameterName2);
        when(stringHelper.truncateToBytes(lowerCaseParameterName3,
                PARAMETER_NAME_MAX_BYTE_LENGTH)).thenReturn(lowerCaseParameterName3);
        when(stringHelper.truncateToBytes(lowerCaseParameterName4,
                PARAMETER_NAME_MAX_BYTE_LENGTH)).thenReturn(lowerCaseParameterName4);
        when(stringHelper.truncateToBytes(lowerCaseParameterName5,
                PARAMETER_NAME_MAX_BYTE_LENGTH)).thenReturn(lowerCaseParameterName5);
        when(stringHelper.truncateToBytes(STRING_VALUE, CLICK_PARAMETER_MAX_BYTE_LENGTH))
                .thenReturn(TRUNCATED_STRING_VALUE);
        when(stringHelper.truncateToBytes(ENCODED_URL, CLICK_PARAMETER_MAX_BYTE_LENGTH))
                .thenReturn(ENCODED_URL);

        // when
        Map<String, String> actual = underTest.getClickParametersFrom(parsedLogItem);

        // then
        assertEquals(5, actual.size());
        assertEquals(TRUNCATED_STRING_VALUE, actual.get(lowerCaseParameterName1));
        assertEquals(TRUNCATED_STRING_VALUE, actual.get(lowerCaseParameterName2));
        assertEquals(DECODED_URL, actual.get(lowerCaseParameterName3));
        assertEquals(DECODED_URL, actual.get(lowerCaseParameterName4));
        assertEquals(TRUNCATED_STRING_VALUE, actual.get(lowerCaseParameterName5));
    }

    @Test
    public void testGetClickParametersFromShouldReturnClickParametersWhenClickParametersAreNotAvailable() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(RESULT_ID, NUMBER_VALUE);
        parsedLogItem.put(CURRENCY, CURRENCY_USD);
        when(stringHelper.truncateToBytes(STRING_VALUE, CLICK_PARAMETER_MAX_BYTE_LENGTH))
                .thenReturn(TRUNCATED_STRING_VALUE);

        // when
        Map<String, String> actual = underTest.getClickParametersFrom(parsedLogItem);

        // then
        assertEquals(0, actual.size());
    }

    @Test
    public void testGetIdentifierFromShouldReturnIdentifierCorrectlyWhenGivenValidIdentifier() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(IDENTIFIER, STRING_VALUE);

        when(underTest.getValidatedValue(parsedLogItem, IDENTIFIER))
                .thenReturn(STRING_VALUE);
        when(stringHelper.truncateToBytesFromFront(STRING_VALUE,
                IDENTIFIER_MAX_BYTE_LENGTH)).thenReturn(STRING_VALUE);

        // when
        String actual = underTest.getIdentifierFrom(parsedLogItem);

        // then
        assertEquals(STRING_VALUE, actual);
    }

    @Test
    public void testGetSessionIdFromShouldReturnSessionIdCorrectlyIdWhenGivenValidSessionId() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(SESSION_ID, STRING_VALUE);

        when(underTest.getValidatedValue(parsedLogItem, SESSION_ID))
                .thenReturn(STRING_VALUE);

        // when
        String actual = underTest.getSessionIdFrom(parsedLogItem);

        // then
        assertEquals(STRING_VALUE, actual);

        verify(logValidator).validateMaxByteCountOf(STRING_VALUE,
                SESSION_ID_MAX_BYTE_LENGTH, SESSION_ID);
    }

    @Test
    public void testGetRequestFromShouldReturnRequestCorrectlyWhenGivenValidRequest() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(REQUEST, STRING_VALUE);
        when(underTest.getValidatedValue(parsedLogItem, REQUEST))
                .thenReturn(STRING_VALUE);

        // when
        String actual = underTest.getRequestFrom(parsedLogItem);

        // then
        assertEquals(STRING_VALUE, actual);
    }

    @Test
    public void testGetValidatedValueShouldReturnCorrectDataWhenGivenValidParameter() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put("PARAMETER_NAME", "PARAMETER_VALUE");

        // when
        String actual = underTest.getValidatedValue(parsedLogItem, "PARAMETER_NAME");

        // then
        assertEquals("PARAMETER_VALUE", actual);
    }

    @Test
    public void testGetValidatedValueShouldThrowExceptionWhenParameterNameDoesNotExist() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();

        // when
        try {
            underTest.getValidatedValue(parsedLogItem, "PARAMTER_NAME");
            fail();

        } catch (TaekkyeonException ex) {
            // then
            assertEquals("The parameter [PARAMTER_NAME] is invalid", ex.getMessage());
        }
    }

    @Test
    public void testGetValidatedValueShouldThrowExceptionWhenParameterValueIsNull() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put("PARAMETER_NAME", null);

        // when
        try {
            underTest.getValidatedValue(parsedLogItem, "PARAMTER_NAME");
            fail();

        } catch (TaekkyeonException ex) {
            // then
            assertEquals("The parameter [PARAMTER_NAME] is invalid", ex.getMessage());
        }
    }

    @Test
    public void testGetValidatedValueShouldThrowExceptionWhenParameterValueIsEmpty() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put("PARAMETER_NAME", "");

        // when
        try {
            underTest.getValidatedValue(parsedLogItem, "PARAMTER_NAME");
            fail();

        } catch (TaekkyeonException ex) {
            // then
            assertEquals("The parameter [PARAMTER_NAME] is invalid", ex.getMessage());
        }
    }

    @Test
    public void testGetTransactionAmountFromShouldReturnTransactionAmountWhenCalled() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put("TRANSACTION_AMOUNT", NUMBER_VALUE);

        // when
        BigDecimal actual = underTest.getTransactionAmountFrom(parsedLogItem);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.intValue());
    }

    @Test
    public void testGetTransactionAmountFromShouldReturnNullWhenGivenEmptyData() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();

        // when
        BigDecimal actual = underTest.getTransactionAmountFrom(parsedLogItem);

        // then
        assertNull(actual);
    }

    @Test
    public void testGetTransactionAmountFromShouldReturnNullWhenGivenValueIsNull() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put("TRANSACTION_AMOUNT", null);

        // when
        BigDecimal actual = underTest.getTransactionAmountFrom(parsedLogItem);

        // then
        assertNull(actual);
    }

    @Test
    public void testGetTransactionAmountFromShouldReturnNullWhenGivenValueIsEmpty() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put("TRANSACTION_AMOUNT", "");

        // when
        BigDecimal actual = underTest.getTransactionAmountFrom(parsedLogItem);

        // then
        assertNull(actual);
    }

    @Test
    public void testGetValidatedValueShouldThrowExceptionWhenParameterValueIsNullString() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put("PARAMETER_NAME", "NULL");

        // when
        try {
            underTest.getValidatedValue(parsedLogItem, "PARAMTER_NAME");
            fail();

        } catch (TaekkyeonException ex) {

            // then
            assertEquals("The parameter [PARAMTER_NAME] is invalid", ex.getMessage());
        }
    }

    @Test
    public void testGetCustomerTypeFromShouldReturnCorrectValueWhenCalled() {
        // given
        Map<String, String> parsedLogItem = ImmutableMap.of(CUSTOMER_TYPE, STRING_VALUE);

        when(stringHelper.truncateToBytes(STRING_VALUE, CUSTOMER_TYPE_MAX_BYTE_LENGTH))
                .thenReturn(TRUNCATED_STRING_VALUE);

        // when
        String actual = underTest.getCustomerTypeFrom(parsedLogItem);

        // then
        assertEquals(TRUNCATED_STRING_VALUE, actual);
    }

    @Test
    public void testGetLanguageFromShouldReturnCorrectValueWhenCalled() {
        // given
        Map<String, String> parsedLogItem = ImmutableMap.of(LANGUAGE, "en_US");

        // when
        String actual = underTest.getLanguageFrom(parsedLogItem);

        // then
        assertEquals("English", actual);
    }

    @Test
    public void testGetClickIpAddressFromShouldReturnTruncatedClickIpAddressWhenCalled() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(CLICK_IP_ADDRESS, STRING_VALUE);

        when(stringHelper.truncateToBytes(STRING_VALUE, IP_ADDRESS_MAX_BYTE_LENGTH))
                .thenReturn(TRUNCATED_STRING_VALUE);

        // when
        String actual = underTest.getClickIpAddressFrom(parsedLogItem);

        // then
        assertEquals(TRUNCATED_STRING_VALUE, actual);
    }

    @Test
    public void testGetCurrencyFromShouldReturnEmptyWhenParsedLogItemIsNotIncludingCurrency() {
        // given
        Map<String, String> parsedLogItem = Collections.emptyMap();

        // when
        String actual = underTest.getCurrencyFrom(parsedLogItem);

        // then
        assertSame(EMPTY, actual);
    }

    @Test
    public void testGetCurrencyFromShouldReturnEmptyWhenParsedLogItemIsIncludingCurrencyAndCurrencyIsEmpty() {
        // given
        Map<String, String> parsedLogItem = ImmutableMap.of(CURRENCY, "");

        // when
        String actual = underTest.getCurrencyFrom(parsedLogItem);

        // then
        assertSame(EMPTY, actual);
    }

    @Test
    public void testGetCurrencyFromShouldReturnEmptyWhenParsedLogItemIsIncludingCurrencyAndCurrencyIsNotCorrect() {
        // given
        Map<String, String> parsedLogItem = ImmutableMap.of(CURRENCY, "UDS");

        // when
        String actual = underTest.getCurrencyFrom(parsedLogItem);

        // then
        assertSame(EMPTY, actual);
    }

    @Test
    public void testGetCurrencyFromShouldReturnCorrectCurrencyWhenParsedLogItemIsIncludingCurrencyAndCurrencyIsCorrectAndLowercase() {
        // given
        Map<String, String> parsedLogItem = ImmutableMap.of(CURRENCY, "usd");

        // when
        String actual = underTest.getCurrencyFrom(parsedLogItem);

        // then
        assertEquals("USD", actual);
    }

    @Test
    public void testGetCurrencyFromShouldReturnCorrectCurrencyWhenParsedLogItemIsIncludingCurrencyAndCurrencyIsCorrectAndUppercase() {
        // given
        Map<String, String> parsedLogItem = ImmutableMap.of(CURRENCY, "USD");

        // when
        String actual = underTest.getCurrencyFrom(parsedLogItem);

        // then
        assertEquals("USD", actual);
    }

    @Test
    public void testDecodeUrlShouldReturnDecodedUrlWhenCalled() {
        // when
        String actual = underTest.decodeUrl(ENCODED_URL);

        // then
        assertEquals(DECODED_URL, actual);
    }
}
