/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.ConversionParameter;

import static org.junit.Assert.assertEquals;

/**
 * Integration test for {@link ConversionParametersMapper}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class ConversionParametersMapperTest {

    @Inject
    private ConversionParametersMapper underTest;

    @Test
    public void testInsertShouldReturnOneWhenGivenValueIsNotNull() {
        // given
        ConversionParameter parameter = new ConversionParameter(1, "transactionId",
                "paramName", "paramValue");

        // when
        int actual = underTest.insert(parameter);

        // then
        assertEquals(1, actual);
    }

    @Test
    public void testInsertShouldReturnOneWhenGivenValueIsNull() {
        // given
        ConversionParameter parameter = new ConversionParameter(2, "transactionId",
                "paramName", null);

        // when
        int actual = underTest.insert(parameter);

        // then
        assertEquals(1, actual);
    }

    @Test
    public void testInsertShouldReturnOneWhenGivenValueIsEmpty() {
        // given
        ConversionParameter parameter = new ConversionParameter(3, "transactionId",
                "paramName", "");

        // when
        int actual = underTest.insert(parameter);

        // then
        assertEquals(1, actual);
    }
}
