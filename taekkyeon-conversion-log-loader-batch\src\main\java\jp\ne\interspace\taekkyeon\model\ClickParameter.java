/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * DTO for holding the data of a click parameter.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter @ToString
public class ClickParameter {

    private final long campaignId;
    private final String internalTransactionId;
    private final String paramName;
    private final String paramValue;
}
