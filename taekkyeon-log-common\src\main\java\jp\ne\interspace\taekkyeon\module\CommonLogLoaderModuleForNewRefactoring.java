/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import com.google.inject.AbstractModule;
import com.google.inject.TypeLiteral;
import org.easybatch.core.processor.RecordProcessor;
import org.easybatch.core.reader.RecordReader;
import org.easybatch.core.record.Record;
import org.easybatch.core.writer.RecordWriter;

import jp.ne.interspace.taekkyeon.batch.processor.LogRecordProcessor;
import jp.ne.interspace.taekkyeon.batch.reader.LogRecordReader;
import jp.ne.interspace.taekkyeon.batch.writer.LogRecordWriter;

/**
 * Common guice module for the new refactoring log loader batches.
 *
 * <AUTHOR>
 */
public class CommonLogLoaderModuleForNewRefactoring extends AbstractModule {

    @Override
    protected void configure() {
        install(new CommonLogPropertiesModuleForNewRefactoring());
        install(new SimpleQueueServiceQueueConsumerModule());

        bind(RecordReader.class).annotatedWith(MainRecordReaderBinding.class)
                .to(LogRecordReader.class);
        bind(new TypeLiteral<RecordProcessor<? extends Record<?>, ? extends Record<?>>>()
        {})
                .annotatedWith(MainRecordProcessorBinding.class)
                .to(LogRecordProcessor.class);
        bind(RecordWriter.class).annotatedWith(MainRecordWriterBinding.class)
                .to(LogRecordWriter.class);
    }
}
