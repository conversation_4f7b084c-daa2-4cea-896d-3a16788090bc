/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * DTO for holding a temporary conversion.
 *
 * <AUTHOR> Shin
 */
@AllArgsConstructor @Getter @ToString
public class IntegrationTestConversion {

    private final Long conversionId;
    private final Long creativeId;
    private final Long campaignId;
    private final LocalDateTime clickDate;
    private final LocalDateTime conversionDate;
    private final LocalDateTime logDate;
    private final LocalDateTime confirmedDate;
    private final String transactionId;
    private final Long siteId;
    private final Integer rank;
    private final String identifier;
    private final Integer resultId;
    private final String productId;
    private final ConversionStatus conversionStatus;
    private final Long quantity;
    private final BigDecimal price;
    private final BigDecimal totalPrice;
    private final RewardType rewardType;
    private final BigDecimal reward;
    private final BigDecimal totalPriceReward;
    private final CommissionType commissionType;
    private final BigDecimal atCommission;
    private final BigDecimal agentCommission;
    private final BigDecimal publisherAgentCommission;
    private final String ipAddress;
    private final String referer;
    private final String userAgent;
    private final LocalDateTime rewardEditDate;
    private final Integer trackingType;
    private final Long defaultQuantity;
    private final BigDecimal defaultPrice;
    private final Integer defaultResultId;
    private final String lpUrl;
    private final DeviceType deviceType;
    private final String pointbackId;
    private final Integer pbIdDuplicativeFlag;
    private final String sessionId;
    private final String uuid;
    private final DeviceOs deviceOs;
    private final String categoryId;
    private final BigDecimal discount;
    private final String internalTransactionId;
    private final BigDecimal originalCurrencyTotalPrice;
    private final String originalCurrency;
    private final String clickReferer;
    private final String clickUrl;
    private final String clickUserAgent;
    private final String customerType;
    private final String language;
    private final String clickIpAddress;
}
