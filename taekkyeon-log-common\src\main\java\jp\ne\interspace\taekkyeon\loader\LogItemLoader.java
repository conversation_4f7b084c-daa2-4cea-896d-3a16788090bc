/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.loader;

import jp.ne.interspace.taekkyeon.model.LogItem;

/**
 * Interface for loading a {@link LogItem} into the Oracle database.
 *
 * <AUTHOR>
 */
public interface LogItemLoader {

    /**
     * Loads {@code logItem} into the database.
     *
     * @param logItem
     *            the {@link LogItem} to load
     */
    void load(LogItem logItem);
}
