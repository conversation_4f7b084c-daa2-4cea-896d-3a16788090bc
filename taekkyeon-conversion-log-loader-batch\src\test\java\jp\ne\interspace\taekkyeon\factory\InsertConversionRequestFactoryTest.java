/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.factory;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.CampaignSettingDuplicationCutDetails;
import jp.ne.interspace.taekkyeon.model.Conversion;
import jp.ne.interspace.taekkyeon.model.CurrencyRate;
import jp.ne.interspace.taekkyeon.model.DeviceOs;
import jp.ne.interspace.taekkyeon.model.DeviceType;
import jp.ne.interspace.taekkyeon.model.InsertConversionRequest;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.AffiliationRankHistoryMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CurrencyMapper;
import jp.ne.interspace.taekkyeon.service.CampaignSettingService;
import jp.ne.interspace.taekkyeon.validator.DatabaseOperationValidator;

import static java.math.BigDecimal.ZERO;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.model.DuplicationCutTarget.DAILY;
import static jp.ne.interspace.taekkyeon.model.DuplicationCutTarget.PERMANENT;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link InsertConversionRequestFactory}.
 *
 * <AUTHOR> Shin
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class InsertConversionRequestFactoryTest {

    private static final LocalDateTime LOG_DATE_TIME = LocalDateTime.of(2017, 10, 11, 15,
            10, 0);
    private static final long CREATIVE_ID = 1L;
    private static final long SITE_ID = 2L;
    private static final long CAMPAIGN_ID = 3L;
    private static final DeviceType DEVICE_TYPE = DeviceType.ANDROID;
    private static final DeviceOs DEVICE_OS = DeviceOs.ANDROID;
    private static final String IP_ADDRESS = "ipAddress";
    private static final String REFERER = "referer";
    private static final String USER_AGENT = "userAgent";
    private static final LocalDateTime CLICK_DATE_TIME = LocalDateTime.of(2017, 10, 10,
            15, 10, 0);
    private static final LocalDateTime CONVERSION_DATE_TIME = LocalDateTime.of(2017, 10,
            11, 15, 10, 5);
    private static final LocalDate CONVERSION_DATE = LocalDate.of(2017, 10, 11);
    private static final YearMonth CONVERSION_MONTH = YearMonth.of(2017, 10);
    private static final String SESSION_ID = "sessionId";
    private static final String UUID = "uuid";
    private static final String IDENTIFIER = "identifier";
    private static final int RESULT_ID = 3;
    private static final BigDecimal PRICE = BigDecimal.valueOf(1100);
    private static final BigDecimal UNIT_PRICE = BigDecimal.valueOf(1000);
    private static final BigDecimal TOTAL_PRICE = BigDecimal.valueOf(3000);
    private static final BigDecimal DISCOUNT = BigDecimal.valueOf(500);
    private static final BigDecimal HIGHER_DISCOUNT = BigDecimal.valueOf(1500);
    private static final BigDecimal MULTIPLE_PRODUCT_HIGHER_DISCOUNT = BigDecimal.valueOf(3000);
    private static final String PRODUCT_ID = "productId";
    private static final String CATEGORY_ID = "categoryId";
    private static final String POINTBACK_ID = "pointbackId";
    private static final String CLICK_REFERER = "clickReferer";
    private static final String CLICK_URL = "clickUrl";
    private static final String CLICK_USER_AGENT = "clickUserAgent";
    private static final Map<String, String> CONVERSION_PARAMETERS = new HashMap<>();
    private static final Map<String, String> CLICK_PARAMETERS = new HashMap<>();
    private static final long QUANTITY_1 = 1L;
    private static final long QUANTITY_2 = 2L;
    private static final List<String> TRANSACTION_IDS = Arrays
            .asList("2017-10-11 15:10:05-identifier-productId");
    private static final List<String> MULTIPLE_PRODUCT_CONVERSION_TRANSACTION_IDS = Arrays
            .asList("2017-10-11 15:10:05-identifier-productId-0",
                    "2017-10-11 15:10:05-identifier-productId-1");
    private static final String CUSTOMER_TYPE = "customerType";
    private static final String LANGUAGE = "language";

    private static final String CURRENCY = "USD";
    private static final BigDecimal CURRENCY_RATE = BigDecimal.valueOf(1.2);
    private static final int RANK = 5;
    private static final String INTERNAL_TRANSACTION_ID = "internalTransactionId";
    private static final String CLICK_IP_ADDRESS = "clickIpAddress";

    @InjectMocks @Spy
    private InsertConversionRequestFactory underTest;

    @Mock
    private CurrencyMapper currencyMapper;

    @Mock
    private CampaignSettingService campaignSettingService;

    @Mock
    private AffiliationRankHistoryMapper affiliationMapper;

    @Mock
    private DatabaseOperationValidator validator;

    @Test
    public void testCreatePermanentTargetInternalTransactionIdShouldReturnCorrectValueWhenCustomerTypeIsNotEmpty() {
        // given
        String expected = "3$identifier$3$customerType";

        // when
        String actual = underTest.createPermanentTargetInternalTransactionId(CAMPAIGN_ID,
                IDENTIFIER, RESULT_ID, CUSTOMER_TYPE);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testCreatePermanentTargetInternalTransactionIdShouldReturnCorrectValueWhenCustomerTypeIsEmpty() {
        // given
        String customerType = "";
        String expected = "3$identifier$3";

        // when
        String actual = underTest.createPermanentTargetInternalTransactionId(CAMPAIGN_ID,
                IDENTIFIER, RESULT_ID, customerType);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testCreatePermanentTargetInternalTransactionIdShouldReturnCorrectValueWhenCustomerTypeIsNull() {
        // given
        String customerType = null;
        String expected = "3$identifier$3";

        // when
        String actual = underTest.createPermanentTargetInternalTransactionId(CAMPAIGN_ID,
                IDENTIFIER, RESULT_ID, customerType);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testCreateInternalTransactionIdByShouldReturnCorrectInternalTransactionIdStartsWithConversionDateWhenDuplicationCutFlagIsEnabledAndTargetIsDaily() {
        // given
        long campaignId = 1L;
        LocalDate conversionDate = LocalDate.of(2017, 10, 15);
        String identifier = "identifier";
        int resultId = 30;
        String permanentTargetInternalTransactionId = "1$identifier$30$cusomterType";
        String expected = "2017-10-15$1$identifier$30$cusomterType";
        CampaignSettingDuplicationCutDetails duplicationCutDetails =
                new CampaignSettingDuplicationCutDetails(true, DAILY);
        when(campaignSettingService.findDuplicationCutDetailsBy(campaignId))
                .thenReturn(duplicationCutDetails);
        doReturn(permanentTargetInternalTransactionId).when(underTest)
                .createPermanentTargetInternalTransactionId(campaignId, identifier,
                        resultId, CUSTOMER_TYPE);

        // when
        String actual = underTest.createInternalTransactionIdBy(campaignId,
                conversionDate, identifier, resultId, CUSTOMER_TYPE);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testCreateInternalTransactionIdByShouldReturnCorrectInternalTransactionIdStartsWithoutConversionDateWhenDuplicationCutFlagIsEnabledAndTargetIsPermanent() {
        // given
        long campaignId = 2L;
        LocalDate conversionDate = LocalDate.of(2017, 10, 16);
        String identifier = "permenentIdentifier";
        int resultId = 30;
        String permanentTargetInternalTransactionId = "2$permenentIdentifier$30";
        String expected = "2$permenentIdentifier$30";
        CampaignSettingDuplicationCutDetails duplicationCutDetails =
                new CampaignSettingDuplicationCutDetails(true, PERMANENT);
        when(campaignSettingService.findDuplicationCutDetailsBy(campaignId))
                .thenReturn(duplicationCutDetails);
        doReturn(permanentTargetInternalTransactionId).when(underTest)
                .createPermanentTargetInternalTransactionId(campaignId, identifier,
                        resultId, CUSTOMER_TYPE);

        // when
        String actual = underTest.createInternalTransactionIdBy(campaignId,
                conversionDate, identifier, resultId, CUSTOMER_TYPE);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testCreateInternalTransactionIdByShouldReturnCorrectInternalTransactionIdWithRandomStringWhenDuplicationCutFlagIsDisabled() {
        // given
        long campaignId = 1L;
        LocalDate conversionDate = LocalDate.of(2017, 10, 15);
        String identifier = "identifier";
        int resultId = 30;
        CampaignSettingDuplicationCutDetails duplicationCutDetails =
                new CampaignSettingDuplicationCutDetails(false, DAILY);
        when(campaignSettingService.findDuplicationCutDetailsBy(campaignId))
                .thenReturn(duplicationCutDetails);

        // when
        String actual = underTest.createInternalTransactionIdBy(campaignId,
                conversionDate, identifier, resultId, CUSTOMER_TYPE);

        // then
        assertEquals(36, actual.length());
        verify(underTest, never()).createPermanentTargetInternalTransactionId(anyLong(),
                anyString(), anyInt(), anyString());
    }

    @Test
    public void testCreateInsertRequestsOfShouldReturnCorrectDataWhenGivenMultipleProduct()
            throws Exception {
        // given
        Conversion conversion = createProductConversion(QUANTITY_2, DISCOUNT,
                MULTIPLE_PRODUCT_CONVERSION_TRANSACTION_IDS);
        CurrencyRate currencyRate = new CurrencyRate(CURRENCY, CURRENCY_RATE);

        doReturn(currencyRate).when(underTest).getCurrencyRateFrom(CURRENCY, CAMPAIGN_ID,
                CONVERSION_MONTH);
        when(affiliationMapper.findRankBy(CAMPAIGN_ID, SITE_ID, CONVERSION_DATE_TIME))
                .thenReturn(RANK);
        doReturn(INTERNAL_TRANSACTION_ID).when(underTest).createInternalTransactionIdBy(
                CAMPAIGN_ID, CONVERSION_DATE, IDENTIFIER, RESULT_ID, CUSTOMER_TYPE);
        BigDecimal discountedPrice = BigDecimal.valueOf(750);
        doReturn(discountedPrice).when(underTest)
                .getDiscountedPriceFrom(conversion);

        // when
        List<InsertConversionRequest> actual = underTest
                .createInsertRequestsOf(conversion);

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertFields(actual.get(0), LOG_DATE_TIME, CREATIVE_ID, SITE_ID, CAMPAIGN_ID,
                DEVICE_TYPE, DEVICE_OS, IP_ADDRESS, REFERER, USER_AGENT, CLICK_DATE_TIME,
                CONVERSION_DATE_TIME, MULTIPLE_PRODUCT_CONVERSION_TRANSACTION_IDS.get(0),
                INTERNAL_TRANSACTION_ID, SESSION_ID, RANK, IDENTIFIER, RESULT_ID,
                BigDecimal.valueOf(900), CURRENCY, discountedPrice, POINTBACK_ID,
                PRODUCT_ID, UUID, CATEGORY_ID, BigDecimal.valueOf(250), CLICK_REFERER,
                CLICK_URL, CLICK_USER_AGENT);
        assertFields(actual.get(1), LOG_DATE_TIME, CREATIVE_ID, SITE_ID, CAMPAIGN_ID,
                DEVICE_TYPE, DEVICE_OS, IP_ADDRESS, REFERER, USER_AGENT, CLICK_DATE_TIME,
                CONVERSION_DATE_TIME, MULTIPLE_PRODUCT_CONVERSION_TRANSACTION_IDS.get(1),
                INTERNAL_TRANSACTION_ID, SESSION_ID, RANK, IDENTIFIER, RESULT_ID,
                BigDecimal.valueOf(900), CURRENCY, discountedPrice, POINTBACK_ID,
                PRODUCT_ID, UUID, CATEGORY_ID, BigDecimal.valueOf(250), CLICK_REFERER,
                CLICK_URL, CLICK_USER_AGENT);
    }

    @Test
    public void testCreateInsertRequestsOfShouldReturnCorrectDataWhenGivenSingleProduct()
            throws Exception {
        // given
        Conversion conversion = createProductConversion(QUANTITY_1, DISCOUNT,
                TRANSACTION_IDS);
        CurrencyRate currencyRate = new CurrencyRate(CURRENCY, CURRENCY_RATE);

        doReturn(currencyRate).when(underTest).getCurrencyRateFrom(CURRENCY, CAMPAIGN_ID,
                CONVERSION_MONTH);
        when(affiliationMapper.findRankBy(CAMPAIGN_ID, SITE_ID, CONVERSION_DATE_TIME))
                .thenReturn(RANK);
        doReturn(INTERNAL_TRANSACTION_ID).when(underTest).createInternalTransactionIdBy(
                CAMPAIGN_ID, CONVERSION_DATE, IDENTIFIER, RESULT_ID, CUSTOMER_TYPE);
        BigDecimal discountedPrice = BigDecimal.valueOf(500);
        doReturn(discountedPrice).when(underTest).getDiscountedPriceFrom(conversion);

        // when
        List<InsertConversionRequest> actual = underTest
                .createInsertRequestsOf(conversion);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertFields(actual.get(0), LOG_DATE_TIME, CREATIVE_ID, SITE_ID, CAMPAIGN_ID,
                DEVICE_TYPE, DEVICE_OS, IP_ADDRESS, REFERER, USER_AGENT, CLICK_DATE_TIME,
                CONVERSION_DATE_TIME, TRANSACTION_IDS.get(0), INTERNAL_TRANSACTION_ID,
                SESSION_ID, RANK, IDENTIFIER, RESULT_ID, BigDecimal.valueOf(600),
                CURRENCY, discountedPrice, POINTBACK_ID, PRODUCT_ID, UUID, CATEGORY_ID,
                DISCOUNT, CLICK_REFERER, CLICK_URL, CLICK_USER_AGENT);
    }

    @Test
    public void testCreateInsertRequestsOfShouldReturnCorrectDataWhenUnitPriceBecomingNegativeForMultipleProduct()
            throws Exception {
        // given
        Conversion conversion = createProductConversion(QUANTITY_2,
                MULTIPLE_PRODUCT_HIGHER_DISCOUNT,
                MULTIPLE_PRODUCT_CONVERSION_TRANSACTION_IDS);
        CurrencyRate currencyRate = new CurrencyRate(CURRENCY, CURRENCY_RATE);

        doReturn(currencyRate).when(underTest).getCurrencyRateFrom(CURRENCY, CAMPAIGN_ID,
                CONVERSION_MONTH);
        when(affiliationMapper.findRankBy(CAMPAIGN_ID, SITE_ID, CONVERSION_DATE_TIME))
                .thenReturn(RANK);
        doReturn(INTERNAL_TRANSACTION_ID).when(underTest).createInternalTransactionIdBy(
                CAMPAIGN_ID, CONVERSION_DATE, IDENTIFIER, RESULT_ID, CUSTOMER_TYPE);
        doReturn(ZERO).when(underTest).getDiscountedPriceFrom(conversion);

        // when
        List<InsertConversionRequest> actual = underTest
                .createInsertRequestsOf(conversion);

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertFields(actual.get(0), LOG_DATE_TIME, CREATIVE_ID, SITE_ID, CAMPAIGN_ID,
                DEVICE_TYPE, DEVICE_OS, IP_ADDRESS, REFERER, USER_AGENT, CLICK_DATE_TIME,
                CONVERSION_DATE_TIME, MULTIPLE_PRODUCT_CONVERSION_TRANSACTION_IDS.get(0),
                INTERNAL_TRANSACTION_ID, SESSION_ID, RANK, IDENTIFIER, RESULT_ID,
                ZERO, CURRENCY, ZERO, POINTBACK_ID, PRODUCT_ID,
                UUID, CATEGORY_ID, BigDecimal.valueOf(1500), CLICK_REFERER, CLICK_URL,
                CLICK_USER_AGENT);
        assertFields(actual.get(1), LOG_DATE_TIME, CREATIVE_ID, SITE_ID, CAMPAIGN_ID,
                DEVICE_TYPE, DEVICE_OS, IP_ADDRESS, REFERER, USER_AGENT, CLICK_DATE_TIME,
                CONVERSION_DATE_TIME, MULTIPLE_PRODUCT_CONVERSION_TRANSACTION_IDS.get(1),
                INTERNAL_TRANSACTION_ID, SESSION_ID, RANK, IDENTIFIER, RESULT_ID,
                ZERO, CURRENCY, ZERO, POINTBACK_ID, PRODUCT_ID,
                UUID, CATEGORY_ID, BigDecimal.valueOf(1500), CLICK_REFERER, CLICK_URL,
                CLICK_USER_AGENT);
    }

    @Test
    public void testCreateInsertRequestsOfShouldReturnCorrectDataWhenGivenUnitPriceBecomingNegativeForSingleProduct()
            throws Exception {
        // given
        Conversion conversion = createProductConversion(QUANTITY_1, HIGHER_DISCOUNT,
                TRANSACTION_IDS);
        CurrencyRate currencyRate = new CurrencyRate(CURRENCY, CURRENCY_RATE);

        doReturn(currencyRate).when(underTest).getCurrencyRateFrom(CURRENCY, CAMPAIGN_ID,
                CONVERSION_MONTH);
        when(affiliationMapper.findRankBy(CAMPAIGN_ID, SITE_ID, CONVERSION_DATE_TIME))
                .thenReturn(RANK);
        doReturn(INTERNAL_TRANSACTION_ID).when(underTest).createInternalTransactionIdBy(
                CAMPAIGN_ID, CONVERSION_DATE, IDENTIFIER, RESULT_ID, CUSTOMER_TYPE);
        doReturn(ZERO).when(underTest).getDiscountedPriceFrom(conversion);

        // when
        List<InsertConversionRequest> actual = underTest
                .createInsertRequestsOf(conversion);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertFields(actual.get(0), LOG_DATE_TIME, CREATIVE_ID, SITE_ID, CAMPAIGN_ID,
                DEVICE_TYPE, DEVICE_OS, IP_ADDRESS, REFERER, USER_AGENT, CLICK_DATE_TIME,
                CONVERSION_DATE_TIME, TRANSACTION_IDS.get(0), INTERNAL_TRANSACTION_ID,
                SESSION_ID, RANK, IDENTIFIER, RESULT_ID, ZERO, CURRENCY,
                ZERO, POINTBACK_ID, PRODUCT_ID, UUID, CATEGORY_ID,
                HIGHER_DISCOUNT, CLICK_REFERER, CLICK_URL, CLICK_USER_AGENT);
    }

    @Test
    public void testGetDiscountFromShouldReturnCorrectResultWhenCalled() {
        // given
        BigDecimal discount = BigDecimal.valueOf(500);
        BigDecimal originalUnitPrice = BigDecimal.valueOf(1000);
        BigDecimal totalPrice = BigDecimal.valueOf(3000);
        Conversion conversion = createConversion(RESULT_ID, QUANTITY_1, PRICE,
                originalUnitPrice, totalPrice, discount, PRODUCT_ID);
        BigDecimal expected = BigDecimal.valueOf(166.67);
        when(underTest.isDiscountable(conversion)).thenReturn(true);
        when(underTest.getOriginalUnitPriceFrom(conversion)).thenReturn(originalUnitPrice);

        // when
        BigDecimal actual = underTest.getDiscountFrom(conversion);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetDiscountFromShouldReturnConversionDiscountWhenDiscountableMethodReturnsFalse() {
        // given
        BigDecimal discount = BigDecimal.valueOf(500);
        BigDecimal originalUnitPrice = BigDecimal.valueOf(1000);
        BigDecimal totalPrice = BigDecimal.valueOf(3000);
        BigDecimal expected = BigDecimal.valueOf(500);
        Conversion conversion = createConversion(RESULT_ID, QUANTITY_1, PRICE,
                originalUnitPrice, totalPrice, discount, PRODUCT_ID);
        when(underTest.isDiscountable(conversion)).thenReturn(false);
        when(underTest.getOriginalUnitPriceFrom(conversion)).thenReturn(originalUnitPrice);

        // when
        BigDecimal actual = underTest.getDiscountFrom(conversion);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testIsDiscountaleFromShouldReturnTrueWhenTotalPriceAndUnitPriceAndQuantityAndDiscountArePositiveNumber() {
        // given
        BigDecimal discount = BigDecimal.valueOf(500);
        BigDecimal originalUnitPrice = BigDecimal.valueOf(1000);
        int quantity = 3;
        BigDecimal totalPrice = BigDecimal.valueOf(3000);
        Conversion conversion = createConversion(RESULT_ID, quantity, PRICE,
                originalUnitPrice, totalPrice, discount, PRODUCT_ID);
        when(underTest.getOriginalUnitPriceFrom(conversion))
                .thenReturn(originalUnitPrice);

        // when
        boolean actual = underTest.isDiscountable(conversion);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsDiscountaleFromShouldReturnFalseWhenTotalPriceIsZero() {
        // given
        BigDecimal discount = BigDecimal.valueOf(500);
        BigDecimal unitPrice = BigDecimal.valueOf(1000);
        int quantity = 3;
        BigDecimal totalPrice = BigDecimal.valueOf(0);
        Conversion conversion = createConversion(RESULT_ID, quantity, PRICE, unitPrice,
                totalPrice, discount, PRODUCT_ID);

        // when
        boolean actual = underTest.isDiscountable(conversion);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsDiscountaleFromShouldReturnFalseWhenTotalPriceIsNegative() {
        // given
        BigDecimal discount = BigDecimal.valueOf(500);
        BigDecimal unitPrice = BigDecimal.valueOf(1000);
        int quantity = 3;
        BigDecimal totalPrice = BigDecimal.valueOf(-1);
        Conversion conversion = createConversion(RESULT_ID, quantity, PRICE, unitPrice,
                totalPrice, discount, PRODUCT_ID);

        // when
        boolean actual = underTest.isDiscountable(conversion);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsDiscountaleFromShouldReturnFalseWhenUnitPriceIsZero() {
        // given
        BigDecimal discount = BigDecimal.valueOf(500);
        BigDecimal unitPrice = BigDecimal.valueOf(0);
        int quantity = 3;
        BigDecimal totalPrice = BigDecimal.valueOf(3000);
        Conversion conversion = createConversion(RESULT_ID, quantity, PRICE, unitPrice,
                totalPrice, discount, PRODUCT_ID);
        when(underTest.getOriginalUnitPriceFrom(conversion)).thenReturn(unitPrice);

        // when
        boolean actual = underTest.isDiscountable(conversion);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsDiscountaleFromShouldReturnFalseWhenUnitPriceIsNegative() {
        // given
        BigDecimal discount = BigDecimal.valueOf(500);
        BigDecimal unitPrice = BigDecimal.valueOf(-1);
        int quantity = 3;
        BigDecimal totalPrice = BigDecimal.valueOf(3000);
        Conversion conversion = createConversion(RESULT_ID, quantity, PRICE, unitPrice,
                totalPrice, discount, PRODUCT_ID);
        when(underTest.getOriginalUnitPriceFrom(conversion)).thenReturn(unitPrice);

        // when
        boolean actual = underTest.isDiscountable(conversion);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsDiscountaleFromShouldReturnFalseWhenQuantityIsZero() {
        // given
        BigDecimal discount = BigDecimal.valueOf(500);
        BigDecimal unitPrice = BigDecimal.valueOf(1000);
        int quantity = 0;
        BigDecimal totalPrice = BigDecimal.valueOf(3000);
        Conversion conversion = createConversion(RESULT_ID, quantity, PRICE, unitPrice,
                totalPrice, discount, PRODUCT_ID);

        // when
        boolean actual = underTest.isDiscountable(conversion);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsDiscountaleFromShouldReturnFalseWhenQuantityIsNegative() {
        // given
        BigDecimal discount = BigDecimal.valueOf(500);
        BigDecimal unitPrice = BigDecimal.valueOf(1000);
        int quantity = -1;
        BigDecimal totalPrice = BigDecimal.valueOf(3000);
        Conversion conversion = createConversion(RESULT_ID, quantity, PRICE, unitPrice,
                totalPrice, discount, PRODUCT_ID);

        // when
        boolean actual = underTest.isDiscountable(conversion);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsDiscountaleFromShouldReturnFalseWhenDiscountIsZero() {
        // given
        BigDecimal discount = BigDecimal.valueOf(0);
        BigDecimal unitPrice = BigDecimal.valueOf(1000);
        int quantity = 3;
        BigDecimal totalPrice = BigDecimal.valueOf(3000);
        Conversion conversion = createConversion(RESULT_ID, quantity, PRICE, unitPrice,
                totalPrice, discount, PRODUCT_ID);

        // when
        boolean actual = underTest.isDiscountable(conversion);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsDiscountaleFromShouldReturnFalseWhenDiscountIsNegative() {
        // given
        BigDecimal discount = BigDecimal.valueOf(-1);
        BigDecimal unitPrice = BigDecimal.valueOf(1000);
        int quantity = 3;
        BigDecimal totalPrice = BigDecimal.valueOf(3000);
        Conversion conversion = createConversion(RESULT_ID, quantity, PRICE, unitPrice,
                totalPrice, discount, PRODUCT_ID);

        // when
        boolean actual = underTest.isDiscountable(conversion);

        // then
        assertFalse(actual);
    }

    @Test
    public void testGetOriginalUnitPriceFromShouldReturnConversionUnitPriceWhenProductIdAndUnitPriceIsNotNullNorEmpty() {
        // given
        BigDecimal unitPrice = BigDecimal.valueOf(1000);
        Conversion conversion = createConversion(RESULT_ID, QUANTITY_1, PRICE, unitPrice,
                TOTAL_PRICE, DISCOUNT, PRODUCT_ID);

        // when
        BigDecimal actual = underTest.getOriginalUnitPriceFrom(conversion);

        // then
        assertNotNull(actual);
        assertEquals(unitPrice, actual);
    }

    @Test
    public void testGetOriginalUnitPriceFromShouldReturnUnitPriceWhenProductIdIsNullAndResultIdIsThree() {
        // given
        BigDecimal unitPrice = BigDecimal.valueOf(1000);
        Conversion conversion = createConversion(RESULT_ID, QUANTITY_1, PRICE, unitPrice,
                TOTAL_PRICE, DISCOUNT, null);

        // when
        BigDecimal actual = underTest.getOriginalUnitPriceFrom(conversion);

        // then
        assertNotNull(actual);
        assertEquals(unitPrice, actual);
    }

    @Test
    public void testGetOriginalUnitPriceFromShouldReturnUnitPriceWhenUnitPriceIsZeroAndResultIdIsThree() {
        // given
        Conversion conversion = createConversion(RESULT_ID, QUANTITY_1, PRICE, ZERO,
                TOTAL_PRICE, DISCOUNT, PRODUCT_ID);

        // when
        BigDecimal actual = underTest.getOriginalUnitPriceFrom(conversion);

        // then
        assertNotNull(actual);
        assertEquals(ZERO, actual);
    }

    @Test
    public void testGetOriginalUnitPriceFromShouldReturnUnitPriceWhenUnitPriceIsZeroAndResultIdIsThirty() {
        // given
        int resultId = 30;
        Conversion conversion = createConversion(resultId, QUANTITY_1, PRICE, ZERO,
                TOTAL_PRICE, DISCOUNT, PRODUCT_ID);

        // when
        BigDecimal actual = underTest.getOriginalUnitPriceFrom(conversion);

        // then
        assertNotNull(actual);
        assertEquals(ZERO, actual);
    }

    @Test
    public void testGetOriginalUnitPriceFromShouldReturnCorrectPriceWhenUnitPriceIsNegativeAndResultIdIsThree() {
        // given
        BigDecimal unitPrice = BigDecimal.valueOf(-100);
        Conversion conversion = createConversion(RESULT_ID, QUANTITY_1, PRICE, unitPrice,
                TOTAL_PRICE, DISCOUNT, PRODUCT_ID);

        // when
        BigDecimal actual = underTest.getOriginalUnitPriceFrom(conversion);

        // then
        assertNotNull(actual);
        assertEquals(ZERO, actual);
    }

    @Test
    public void testGetOriginalUnitPriceFromShouldReturnCorrectPriceWhenUnitPriceIsNegativeAndResultIdIsThirty() {
        // given
        BigDecimal unitPrice =  BigDecimal.valueOf(-100);
        int resultId = 30;
        Conversion conversion = createConversion(resultId, QUANTITY_1, PRICE, unitPrice,
                TOTAL_PRICE, DISCOUNT, PRODUCT_ID);

        // when
        BigDecimal actual = underTest.getOriginalUnitPriceFrom(conversion);

        // then
        assertNotNull(actual);
        assertEquals(ZERO, actual);
    }

    @Test
    public void testGetOriginalUnitPriceFromShouldReturnConversionPriceWhenUnitPriceIsZeroAndResultIdIsNotThreeOrThirty() {
        // given
        int resultId = 2;
        Conversion conversion = createConversion(resultId, QUANTITY_1, PRICE, ZERO,
                TOTAL_PRICE, DISCOUNT, PRODUCT_ID);

        // when
        BigDecimal actual = underTest.getOriginalUnitPriceFrom(conversion);

        // then
        assertNotNull(actual);
        assertEquals(PRICE, actual);
    }

    @Test
    public void testGetOriginalUnitPriceFromShouldReturnConversionPriceWhenUnitPriceIsZeroAndProductIdIsNotNull() {
        // given
        Conversion conversion = createConversion(RESULT_ID, QUANTITY_1, ZERO, ZERO,
                TOTAL_PRICE, DISCOUNT, PRODUCT_ID);

        // when
        BigDecimal actual = underTest.getOriginalUnitPriceFrom(conversion);

        // then
        assertNotNull(actual);
        assertEquals(ZERO, actual);
    }

    @Test
    public void testGetDiscountedPriceFromShouldReturnCalculatedDiscountedPriceWhenCalculationIsNonNegative() {
        // given
        BigDecimal expectedDiscountPrice = BigDecimal.valueOf(500);
        BigDecimal originalTotalPrice = UNIT_PRICE.multiply(BigDecimal.valueOf(
                QUANTITY_1));
        Conversion conversion = createConversion(RESULT_ID, QUANTITY_1, ZERO, UNIT_PRICE,
                originalTotalPrice, DISCOUNT, PRODUCT_ID);

        // when
        BigDecimal actual = underTest.getDiscountedPriceFrom(conversion);

        // then
        assertNotNull(actual);
        assertEquals(expectedDiscountPrice.doubleValue(), actual.doubleValue(), 0);
    }

    @Test
    public void testGetDiscountedPriceFromShouldReturnDiscountedPriceZeroWhenCalculationIsNegative() {
        // given
        BigDecimal originalTotalPrice = UNIT_PRICE.multiply(BigDecimal.valueOf(
                QUANTITY_1));
        Conversion conversion = createConversion(RESULT_ID, QUANTITY_1, PRICE, UNIT_PRICE,
                originalTotalPrice, HIGHER_DISCOUNT, PRODUCT_ID);

        // when
        BigDecimal actual = underTest.getDiscountedPriceFrom(conversion);

        // then
        assertNotNull(actual);
        assertEquals(ZERO, actual);
    }

    @Test
    public void testGetCurrencyRateFromShouldReturnCorrectCurrencyRateWhenCurrencyIsEmpty()
            throws ExecutionException {
        // given
        CurrencyRate currencyRate = mock(CurrencyRate.class);
        when(currencyMapper.findCampaignCurrencyRate(CAMPAIGN_ID, CONVERSION_MONTH))
                .thenReturn(currencyRate);

        // when
        CurrencyRate actual = underTest.getCurrencyRateFrom(EMPTY, CAMPAIGN_ID,
                CONVERSION_MONTH);

        // then
        assertSame(currencyRate, actual);
        verify(currencyMapper, never()).findCurrencyRate(anyString(), anyLong(),
                any(YearMonth.class));
    }

    @Test
    public void testGetCurrencyRateFromShouldReturnCorrectCurrencyRateWhenGivenCurrencyIsNotEmptyAndCurrencyRateIsNull()
            throws ExecutionException {
        // given
        CurrencyRate currencyRate = new CurrencyRate(null, CURRENCY_RATE);
        when(currencyMapper.findCurrencyRate(CURRENCY, CAMPAIGN_ID, CONVERSION_MONTH))
                .thenReturn(currencyRate);
        CurrencyRate campaigncurrencyRate = mock(CurrencyRate.class);
        when(currencyMapper.findCampaignCurrencyRate(CAMPAIGN_ID, CONVERSION_MONTH))
                .thenReturn(campaigncurrencyRate);

        // when
        CurrencyRate actual = underTest.getCurrencyRateFrom(CURRENCY, CAMPAIGN_ID,
                CONVERSION_MONTH);

        // then
        assertSame(campaigncurrencyRate, actual);
    }

    @Test
    public void testGetCurrencyRateFromShouldReturnCorrectCurrencyRateWhenGivenCurrencyIsNotEmptyAndCurrencyRateIsNotNull()
            throws ExecutionException {
        // given
        CurrencyRate currencyRate = new CurrencyRate(CURRENCY, CURRENCY_RATE);
        when(currencyMapper.findCurrencyRate(CURRENCY, CAMPAIGN_ID, CONVERSION_MONTH))
                .thenReturn(currencyRate);

        // when
        CurrencyRate actual = underTest.getCurrencyRateFrom(CURRENCY, CAMPAIGN_ID,
                CONVERSION_MONTH);

        // then
        assertSame(currencyRate, actual);
        verify(currencyMapper, never()).findCampaignCurrencyRate(anyLong(),
                any(YearMonth.class));
    }

    private void assertFields(InsertConversionRequest actual, LocalDateTime logDate,
            long creativeId, long siteId, long campaignId, DeviceType deviceType,
            DeviceOs deviceOs, String ipAddress, String referer, String userAgent,
            LocalDateTime clickDate, LocalDateTime conversionDate, String transactionId,
            String internalTransactionId, String sessionId, int rank, String identifier,
            int resultId, BigDecimal defaultPrice, String originalCurrency,
            BigDecimal originalCurrencyTotalPrice, String pointbackId, String productId,
            String uuid, String categoryId, BigDecimal discount, String clickReferer,
            String clickUrl, String clickUserAgent) {
        assertNotNull(actual);
        assertEquals(logDate, actual.getLogDate());
        assertEquals(creativeId, actual.getCreativeId());
        assertEquals(siteId, actual.getSiteId());
        assertEquals(campaignId, actual.getCampaignId());
        assertEquals(deviceType, actual.getDeviceType());
        assertEquals(deviceOs, actual.getDeviceOs());
        assertEquals(ipAddress, actual.getIpAddress());
        assertEquals(referer, actual.getReferer());
        assertEquals(userAgent, actual.getUserAgent());
        assertEquals(clickDate, actual.getClickDate());
        assertEquals(conversionDate, actual.getConversionDate());
        assertEquals(transactionId, actual.getTransactionId());
        assertEquals(internalTransactionId, actual.getInternalTransactionId());
        assertEquals(sessionId, actual.getSessionId());
        assertEquals(rank, actual.getRank());
        assertEquals(identifier, actual.getIdentifier());
        assertEquals(resultId, actual.getResultId());
        assertEquals(defaultPrice.doubleValue(), actual.getDefaultPrice().doubleValue(),
                0);
        assertEquals(originalCurrency, actual.getOriginalCurrency());
        assertEquals(originalCurrencyTotalPrice.doubleValue(),
                actual.getOriginalCurrencyTotalPrice().doubleValue(), 0);
        assertEquals(pointbackId, actual.getPointbackId());
        assertEquals(productId, actual.getProductId());
        assertEquals(uuid, actual.getUuid());
        assertEquals(categoryId, actual.getCategoryId());
        assertEquals(discount.doubleValue(), actual.getDiscount().doubleValue(), 0);
        assertEquals(clickReferer, actual.getClickReferer());
        assertEquals(clickUrl, actual.getClickUrl());
        assertEquals(clickUserAgent, actual.getClickUserAgent());
        assertEquals(CUSTOMER_TYPE, actual.getCustomerType());
        assertEquals(LANGUAGE, actual.getLanguage());
    }

    private Conversion createProductConversion(long quantity, BigDecimal discount,
            List<String> transactionIds) {
        return new Conversion(LOG_DATE_TIME, CREATIVE_ID, SITE_ID, CAMPAIGN_ID,
                DEVICE_TYPE, DEVICE_OS, IP_ADDRESS, REFERER, USER_AGENT,
                CLICK_DATE_TIME, CONVERSION_DATE_TIME, SESSION_ID, UUID, IDENTIFIER,
                RESULT_ID, quantity, PRICE, UNIT_PRICE,
                UNIT_PRICE.multiply(BigDecimal.valueOf(quantity)), discount, PRODUCT_ID,
                CATEGORY_ID, POINTBACK_ID, CLICK_REFERER, CLICK_URL, CLICK_USER_AGENT,
                CONVERSION_PARAMETERS, CLICK_PARAMETERS, transactionIds, CUSTOMER_TYPE,
                LANGUAGE, CLICK_IP_ADDRESS, CURRENCY);
    }

    private Conversion createConversion(int resultId, long quantity, BigDecimal price,
            BigDecimal originalUnitPrice, BigDecimal totalPrice, BigDecimal discount,
            String productId) {
        return new Conversion(LOG_DATE_TIME, CREATIVE_ID, SITE_ID, CAMPAIGN_ID,
                DEVICE_TYPE, DEVICE_OS, IP_ADDRESS, REFERER, USER_AGENT, CLICK_DATE_TIME,
                CONVERSION_DATE_TIME, SESSION_ID, UUID, IDENTIFIER, resultId, quantity,
                price, originalUnitPrice, totalPrice, discount, productId, CATEGORY_ID,
                POINTBACK_ID, CLICK_REFERER, CLICK_URL, CLICK_USER_AGENT,
                CONVERSION_PARAMETERS, CLICK_PARAMETERS, TRANSACTION_IDS, CUSTOMER_TYPE,
                LANGUAGE, CLICK_IP_ADDRESS, CURRENCY);
    }
}
