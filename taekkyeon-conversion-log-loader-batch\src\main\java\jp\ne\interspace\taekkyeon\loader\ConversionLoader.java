/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.loader;

import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionManager;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;
import jp.ne.interspace.taekkyeon.factory.InsertConversionRequestFactory;
import jp.ne.interspace.taekkyeon.model.ClickParameter;
import jp.ne.interspace.taekkyeon.model.Conversion;
import jp.ne.interspace.taekkyeon.model.ConversionInsertion;
import jp.ne.interspace.taekkyeon.model.ConversionParameter;
import jp.ne.interspace.taekkyeon.model.InsertConversionRequest;
import jp.ne.interspace.taekkyeon.model.LogItem;
import jp.ne.interspace.taekkyeon.module.OracleResolver;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.MyBatisSessionManagerRepository;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CampaignMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ClickParametersMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ConversionMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ConversionParametersMapper;
import jp.ne.interspace.taekkyeon.service.ConversionInsertionService;
import jp.ne.interspace.taekkyeon.validator.DatabaseOperationValidator;

import static com.google.common.base.Splitter.on;
import static com.google.common.base.Strings.nullToEmpty;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.COMMA;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.ENABLE_MULTIPLE_DATABASES;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.TARGET_COUNTRY;
import static org.apache.ibatis.session.TransactionIsolationLevel.READ_COMMITTED;

/**
 * {@link LogItemLoader} implementation for {@link Conversion}s.
 *
 * <AUTHOR> Shin
 */
@Singleton @Slf4j
public class ConversionLoader implements LogItemLoader {

    @Inject
    private ConversionMapper conversionMapper;

    @Inject
    private ClickParametersMapper clickParametersMapper;

    @Inject
    private ConversionParametersMapper conversionParametersMapper;

    @Inject
    private CampaignMapper campaignMapper;

    @Inject
    private ConversionInsertionService conversionInsertionService;

    @Inject
    private DatabaseOperationValidator validator;

    @Inject
    private InsertConversionRequestFactory insertConversionRequestFactory;

    @Inject(optional = true) @OracleResolver
    private SqlSessionManager sqlSessionManager;

    @Inject(optional = true)
    private MyBatisSessionManagerRepository sessionManagerRepository;

    private LoadingCache<Long, String> refererCheckerCache = CacheBuilder.newBuilder()
            .maximumSize(5000).build(new CacheLoader<Long, String>() {

                @Override
                public String load(Long key) throws Exception {
                    return nullToEmpty(campaignMapper.findRefererCheckerBy(key));
                }
            });

    @Override
    public void load(LogItem logItem) {
        try {
            String referer = logItem.getReferer();
            Long campaignId = logItem.getCampaignId();
            if (isBypassable(campaignId) || isAllowed(referer, campaignId)) {
                insert(logItem);
            }
        } catch (Exception e) {
            throw new TaekkyeonException(e.getMessage());
        }
    }

    @VisibleForTesting
    boolean isAllowed(String referer, Long campaignId) throws Exception {
        String refererChecker = refererCheckerCache.get(campaignId);
        boolean isAllowedReferer = false;
        List<String> splitCheckers = on(COMMA).splitToList(refererChecker);
        for (String checker : splitCheckers) {
            if (nullToEmpty(referer).contains(checker)) {
                isAllowedReferer = true;
                break;
            }
        }
        return isAllowedReferer;
    }

    @VisibleForTesting
    int insertConversion(ConversionInsertion conversionInsertion) {
        int insertedConversionCount = 0;
        try {
            insertedConversionCount = conversionMapper.insert(conversionInsertion);
            getLogger().info("ConversionInsertion has been successfully inserted: {}",
                    conversionInsertion);
        } catch (Exception ex) {
            getLogger().error("Insert ConversionRequest is failed: {}\n{}",
                    conversionInsertion, ex);
        }
        return insertedConversionCount;
    }

    @VisibleForTesting
    void insertConversionParameters(Map<String, String> conversionParameters,
            long campaignId, String transactionId) {
        for (Entry<String, String> entry : conversionParameters.entrySet()) {
            ConversionParameter conversionParameter = new ConversionParameter(campaignId,
                    transactionId, entry.getKey(), entry.getValue());
            try {
                int insertedConversionParameterCount = conversionParametersMapper
                        .insert(conversionParameter);
                validator.validateInsertedRowCount(insertedConversionParameterCount);
                getLogger().info("ConversionParameter has been successfully inserted. {}",
                        conversionParameter);
            } catch (Exception ex) {
                getLogger().info("Insert ConversionParameter is failed. {}\n{}",
                        conversionParameter, ex);
                throw ex;
            }
        }
    }

    @VisibleForTesting
    void insertClickParameters(Map<String, String> clickParameters, long campaignId,
            String internalTransactionId) {
        for (Entry<String, String> entry : clickParameters.entrySet()) {
            ClickParameter clickParameter = new ClickParameter(campaignId,
                    internalTransactionId, entry.getKey(), entry.getValue());
            try {
                clickParametersMapper.insert(clickParameter);
                getLogger().info("ClickParameter has been successfully inserted. {}",
                        clickParameter);
            } catch (Exception ex) {
                getLogger().error("Insert ClickParameter is failed. {}\n{}",
                        clickParameter, ex);
            }
        }
    }

    @VisibleForTesting
    Logger getLogger() {
        return log;
    }

    @VisibleForTesting
    SqlSessionManager getSqlSessionManager() {
        return ENABLE_MULTIPLE_DATABASES
                ? sessionManagerRepository.getSessionManagerOf(TARGET_COUNTRY)
                : sqlSessionManager;
    }

    private void insert(LogItem logItem) throws Exception {
        Conversion conversion = (Conversion) logItem;
        List<InsertConversionRequest> conversionRequests = insertConversionRequestFactory
                .createInsertRequestsOf(conversion);
        List<ConversionInsertion> conversionInsertions = conversionInsertionService
                .convert(conversionRequests);
        if (conversionInsertions.size() > 0) {
            SqlSessionManager sessionManager = getSqlSessionManager();
            sessionManager.startManagedSession(READ_COMMITTED);
            try {
                for (ConversionInsertion conversionInsertion : conversionInsertions) {
                    int insertedCount = insertConversion(conversionInsertion);
                    if (insertedCount > 0) {
                        insertConversionParameters(conversion.getConversionParameters(),
                                conversionInsertion.getCampaignId(),
                                conversionInsertion.getTransactionId());
                        insertClickParameters(conversion.getClickParameters(),
                                conversionInsertion.getCampaignId(),
                                conversionInsertion.getInternalTransactionId());
                    }
                }
                sessionManager.commit();
            } catch (Exception e) {
                log.error("Failed to insert conversion data. " + conversionInsertions, e);
                sessionManager.rollback();
            } finally {
                sessionManager.close();
            }
        }
    }

    private boolean isBypassable(Long campaignId) throws Exception {
        return Strings.isNullOrEmpty(refererCheckerCache.get(campaignId));
    }
}
