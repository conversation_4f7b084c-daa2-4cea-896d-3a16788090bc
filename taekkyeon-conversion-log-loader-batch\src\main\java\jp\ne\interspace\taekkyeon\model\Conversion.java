/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * DTO for holding the data of a conversion.
 *
 * <AUTHOR>
 */
@Getter @Setter @ToString(callSuper = true)
public class Conversion extends LogItem {

    private final LocalDateTime clickDate;
    private final LocalDateTime conversionDate;
    private final String sessionId;
    private final String uuid;
    private final String identifier;
    private final int resultId;
    private final long quantity;
    private final BigDecimal price;
    private final BigDecimal unitPrice;
    private final BigDecimal originalTotalPrice;
    private final String productId;
    private final String categoryId;
    private final String pointbackId;
    private final BigDecimal discount;
    private final String clickReferer;
    private final String clickUrl;
    private final String clickUserAgent;
    private final Map<String, String> conversionParameters;
    private final Map<String, String> clickParameters;
    private final List<String> transactionIds;
    private final String customerType;
    private final String language;
    private final String clickIpAddress;
    private final String currency;

    /**
     * Constructor to be used by the relevant MyBatis mapper. Cannot be auto-generated
     * because of the {@code super()} call.
     */
    public Conversion(LocalDateTime logDate, long creativeId, long siteId,
            long campaignId, DeviceType deviceType, DeviceOs deviceOs, String ipAddress,
            String referer, String userAgent, LocalDateTime clickDate,
            LocalDateTime conversionDate, String sessionId, String uuid,
            String identifier, int resultId, long quantity, BigDecimal price,
            BigDecimal unitPrice, BigDecimal originalTotalPrice, BigDecimal discount,
            String productId, String categoryId, String pointbackId, String clickReferer,
            String clickUrl, String clickUserAgent,
            Map<String, String> conversionParameters, Map<String, String> clickParameters,
            List<String> transactionIds, String customerType, String language,
            String clickIpAddress, String currency) {

        super(logDate, creativeId, siteId, campaignId, deviceType, deviceOs, ipAddress,
                referer, userAgent);
        this.clickDate = clickDate;
        this.conversionDate = conversionDate;
        this.sessionId = sessionId;
        this.uuid = uuid;
        this.identifier = identifier;
        this.resultId = resultId;
        this.quantity = quantity;
        this.price = price;
        this.productId = productId;
        this.categoryId = categoryId;
        this.pointbackId = pointbackId;
        this.discount = discount;
        this.unitPrice = unitPrice;
        this.originalTotalPrice = originalTotalPrice;
        this.clickReferer = clickReferer;
        this.clickUrl = clickUrl;
        this.clickUserAgent = clickUserAgent;
        this.conversionParameters = conversionParameters;
        this.clickParameters = clickParameters;
        this.transactionIds = transactionIds;
        this.customerType = customerType;
        this.language = language;
        this.clickIpAddress = clickIpAddress;
        this.currency = currency;
    }
}
