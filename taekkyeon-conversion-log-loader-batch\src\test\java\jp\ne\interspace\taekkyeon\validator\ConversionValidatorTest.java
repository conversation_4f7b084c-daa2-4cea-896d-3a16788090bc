/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.validator;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ResultTargetSettingMapper;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;
import static org.mockito.Mockito.doReturn;

/**
 * Unit test for {@link ConversionValidator}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class ConversionValidatorTest {

    @InjectMocks
    private ConversionValidator underTest;

    @Mock
    private ResultTargetSettingMapper resultTargetSettingMapper;

    @Test
    public void testValidateResultIdShouldNotThrowTaekkyeonExceptionWhenGivenResultIdOfGivenCampaignIdExits() {
        // given
        int resultId = 10;
        long campaignId = 100;
        doReturn(true).when(resultTargetSettingMapper).isResultIdAvailable(resultId,
                campaignId);

        // when
        underTest.validateResultId(resultId, campaignId);
    }

    @Test
    public void testValidateResultIdShouldThrowTaekkyeonExceptionWhenGivenResultIdOfGivenCampaignIdDoesNotExist() {
        // given
        int resultId = 1000000;
        long campaignId = 100;
        doReturn(false).when(resultTargetSettingMapper).isResultIdAvailable(resultId,
                campaignId);
        String expectedMessage = "Result Id 1000000 is not found for campaign Id 100";

        // when
        try {
            underTest.validateResultId(resultId, campaignId);
            fail();
        } catch (TaekkyeonException ex) {
            // then
            assertEquals(expectedMessage, ex.getMessage());
        }
    }
}
