/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import javax.inject.Singleton;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;

import static com.google.common.base.Strings.isNullOrEmpty;
import static java.lang.Integer.parseInt;
import static java.lang.String.format;
import static java.math.BigDecimal.ZERO;
import static org.apache.commons.lang3.math.NumberUtils.isNumber;

/**
 * Serivce for parsing conversion request.
 *
 * <AUTHOR> Shin
 */
@Singleton
public class ConversionRequestParserService {

    private static final String REQUEST_FIELD_SEPARATOR = "&";
    private static final String REQUEST_VALUE_SEPARATOR = ":";

    private static final String RESULT_ID = "RESULT_ID";
    private static final String VALUE = "VALUE";
    private static final String PARAMETER_INVALID_MESSAGE_FORMAT = "The parameter [%s] is invalid";

    /**
     * Returns {@code request} in a form of key-value pairs for each field inside it.
     *
     * @param request
     *            the log item from request parameter
     * @return {@code request} in a form of key-value pairs for each field inside it
     */
    public Map<String, String> parseRequest(String request) {
        Map<String, String> parsedLogItem = new HashMap<>();
        if (!isNullOrEmpty(request)) {
            String[] rawFields = request.split(REQUEST_FIELD_SEPARATOR);
            for (int i = 0; i < rawFields.length; i++) {
                String[] field = rawFields[i].split(REQUEST_VALUE_SEPARATOR);
                parsedLogItem.put(field[0].toUpperCase(),
                        field.length > 1 ? field[1] : null);
            }
        }
        return parsedLogItem;
    }

    /**
     * Returns the parsed {@code resultId} of the given request, if not contains
     * {@code resultId}, return 0.
     *
     * @param parsedRequest
     *            the given log item
     * @return the parsed {@code resultId} of the given request if not contains
     *         {@code resultId}, return 0
     */
    public int getResultIdFrom(Map<String, String> parsedRequest) {
        int resultId = parseInt(parsedRequest.get(RESULT_ID));
        if (resultId >= 0) {
            return resultId;
        } else {
            throw new TaekkyeonException(
                    format(PARAMETER_INVALID_MESSAGE_FORMAT, RESULT_ID));
        }
    }

    /**
     * Returns the parsed {@code price} of the given request,
     * if not contains {@code price}, return {@link BigDecimal.ZERO}.
     *
     * @param parsedRequest
     *            the given log item
     * @return the parsed {@code price} of the given request
     *          if not contains {@code price}, return {@link BigDecimal.ZERO}
     */
    public BigDecimal getPriceFrom(Map<String, String> parsedRequest) {
        String price = parsedRequest.get(VALUE);
        return isNumber(price) ? new BigDecimal(price) : ZERO;
    }
}
