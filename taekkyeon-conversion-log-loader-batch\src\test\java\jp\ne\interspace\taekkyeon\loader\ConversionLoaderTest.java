/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.loader;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.session.SqlSessionManager;
import org.apache.ibatis.session.TransactionIsolationLevel;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.factory.InsertConversionRequestFactory;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.ClickParameter;
import jp.ne.interspace.taekkyeon.model.Conversion;
import jp.ne.interspace.taekkyeon.model.ConversionInsertion;
import jp.ne.interspace.taekkyeon.model.ConversionParameter;
import jp.ne.interspace.taekkyeon.model.DeviceOs;
import jp.ne.interspace.taekkyeon.model.DeviceType;
import jp.ne.interspace.taekkyeon.model.InsertConversionRequest;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CampaignMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ClickParametersMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ConversionMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ConversionParametersMapper;
import jp.ne.interspace.taekkyeon.service.ConversionInsertionService;
import jp.ne.interspace.taekkyeon.validator.DatabaseOperationValidator;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link ConversionLoader}.
 *
 * <AUTHOR> Varga
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class ConversionLoaderTest {

    private static final LocalDateTime LOG_DATE_TIME = LocalDateTime.of(2017, 10, 11, 15,
            10, 0);
    private static final long CREATIVE_ID = 1L;
    private static final long SITE_ID = 2L;
    private static final long CAMPAIGN_ID = 3L;
    private static final DeviceType DEVICE_TYPE = DeviceType.ANDROID;
    private static final DeviceOs DEVICE_OS = DeviceOs.ANDROID;
    private static final String IP_ADDRESS = "ipAddress";
    private static final String REFERER = "referer";
    private static final String USER_AGENT = "userAgent";
    private static final LocalDateTime CLICK_DATE_TIME = LocalDateTime.of(2017, 10, 10,
            15, 10, 0);
    private static final LocalDateTime CONVERSION_DATE_TIME = LocalDateTime.of(2017, 10,
            11, 15, 10, 5);
    private static final String SESSION_ID = "sessionId";
    private static final String UUID = "uuid";
    private static final String IDENTIFIER = "identifier";
    private static final int RESULT_ID = 3;
    private static final BigDecimal PRICE = BigDecimal.valueOf(10);
    private static final BigDecimal UNIT_PRICE = BigDecimal.valueOf(11);
    private static final BigDecimal DISCOUNT = BigDecimal.valueOf(9);
    private static final String PRODUCT_ID = "productId";
    private static final String CATEGORY_ID = "categoryId";
    private static final String POINTBACK_ID = "pointbackId";
    private static final String CLICK_REFERER = "clickReferer";
    private static final String CLICK_URL = "clickUrl";
    private static final String CLICK_USER_AGENT = "clickUserAgent";
    private static final String CUSTOMER_TYPE = "customerType";
    private static final Map<String, String> CONVERSION_PARAMETERS = new HashMap<>();
    private static final Map<String, String> CLICK_PARAMETERS = new HashMap<>();
    private static final List<String> TRANSACTION_IDS = new ArrayList<>();

    private static final String CURRENCY = "USD";
    private static final String LANGUAGE = "language";
    private static final String CLICK_IP_ADDRESS = "clickIpAddress";

    @InjectMocks @Spy
    private ConversionLoader underTest;

    @Mock
    private ConversionMapper conversionMapper;

    @Mock
    private ClickParametersMapper clickParametersMapper;

    @Mock
    private ConversionParametersMapper conversionParametersMapper;

    @Mock
    private CampaignMapper campaignMapper;

    @Mock
    private DatabaseOperationValidator validator;

    @Mock
    private InsertConversionRequestFactory insertConversionRequestFactory;

    @Mock
    private ConversionInsertionService conversionInsertionService;

    @Mock
    private SqlSessionManager sessionManager;

    @Mock
    private Logger logger;

    @Test
    public void testInsertConversionShouldCallInsertWithValidatorWhenCalled() {
        // given
        ConversionInsertion conversionInsertion = mock(ConversionInsertion.class);
        when(conversionMapper.insert(conversionInsertion)).thenReturn(1);
        doReturn(logger).when(underTest).getLogger();

        // when
        underTest.insertConversion(conversionInsertion);

        // then
        verify(logger).info("ConversionInsertion has been successfully inserted: {}",
                conversionInsertion);
    }

    @Test
    public void testInsertConversionParametersShouldCallInsertWithValidatorWhenCalled() {
        // given
        Map<String, String> conversionParameters = new HashMap<>();
        conversionParameters.put("key1", "value1");
        conversionParameters.put("key2", "value2");
        String transactionId = "transactionId";
        ArgumentCaptor<ConversionParameter> captor = ArgumentCaptor
                .forClass(ConversionParameter.class);
        when(conversionParametersMapper.insert(captor.capture())).thenReturn(1);
        doReturn(logger).when(underTest).getLogger();

        // when
        underTest.insertConversionParameters(conversionParameters, CAMPAIGN_ID,
                transactionId);

        // then
        List<ConversionParameter> actual = captor.getAllValues();
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertFields(actual.get(0), transactionId, "key1", "value1");
        assertFields(actual.get(1), transactionId, "key2", "value2");

        verify(conversionParametersMapper).insert(actual.get(0));
        verify(conversionParametersMapper).insert(actual.get(1));
        verify(validator, times(2)).validateInsertedRowCount(1);
        verify(logger).info("ConversionParameter has been successfully inserted. {}",
                actual.get(0));
        verify(logger).info("ConversionParameter has been successfully inserted. {}",
                actual.get(1));
    }

    @Test
    public void testInsertClickParametersShouldCallInsertWithValidatorWhenCalled() {
        // given
        Map<String, String> clickParameters = new HashMap<>();
        clickParameters.put("key1", "value1");
        clickParameters.put("key2", "value2");
        String internalTransactionId = "internalTransactionId";
        ArgumentCaptor<ClickParameter> captor = ArgumentCaptor
                .forClass(ClickParameter.class);
        when(clickParametersMapper.insert(captor.capture())).thenReturn(1);
        doReturn(logger).when(underTest).getLogger();

        // when
        underTest.insertClickParameters(clickParameters, CAMPAIGN_ID,
                internalTransactionId);

        // then
        List<ClickParameter> actual = captor.getAllValues();
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertFields(actual.get(0), internalTransactionId, "key1", "value1");
        assertFields(actual.get(1), internalTransactionId, "key2", "value2");

        verify(clickParametersMapper).insert(actual.get(0));
        verify(clickParametersMapper).insert(actual.get(1));
        verify(logger).info("ClickParameter has been successfully inserted. {}",
                actual.get(0));
        verify(logger).info("ClickParameter has been successfully inserted. {}",
                actual.get(1));
    }

    @Test
    public void testLoadShouldCallInsertMethodsWhenCalled() throws Exception {
        // given
        Conversion conversion = createConversion(1);
        InsertConversionRequest conversionRequest = createInsertConversionRequest();
        List<InsertConversionRequest> conversionRequests = new ArrayList<>();
        conversionRequests.add(conversionRequest);
        ConversionInsertion conversionInsertion = mock(ConversionInsertion.class);
        List<ConversionInsertion> conversionInsertions = Arrays
                .asList(conversionInsertion);
        when(conversionInsertion.getCampaignId()).thenReturn(CAMPAIGN_ID);
        when(conversionInsertion.getTransactionId()).thenReturn("transactionId");
        when(conversionInsertion.getInternalTransactionId()).thenReturn(
                "internalTransactionId");
        when(conversionInsertionService.convert(conversionRequests))
                .thenReturn(conversionInsertions);
        doReturn(sessionManager).when(underTest).getSqlSessionManager();
        doReturn(true).when(underTest).isAllowed(conversion.getReferer(),
                conversion.getCampaignId());
        doReturn(conversionRequests).when(insertConversionRequestFactory)
                .createInsertRequestsOf(conversion);
        doReturn(1).when(underTest).insertConversion(conversionInsertion);
        doNothing().when(underTest).insertConversionParameters(
                conversion.getConversionParameters(), conversion.getCampaignId(),
                conversionRequest.getTransactionId());
        doNothing().when(underTest).insertClickParameters(conversion.getClickParameters(),
                conversion.getCampaignId(), conversionRequest.getInternalTransactionId());

        // when
        underTest.load(conversion);

        // then
        verify(insertConversionRequestFactory).createInsertRequestsOf(conversion);
        verify(underTest).insertConversion(conversionInsertions.get(0));
        verify(underTest).insertConversionParameters(conversion.getConversionParameters(),
                conversion.getCampaignId(), conversionRequest.getTransactionId());
        verify(underTest).insertClickParameters(conversion.getClickParameters(),
                conversion.getCampaignId(), conversionRequest.getInternalTransactionId());
        verify(sessionManager)
                .startManagedSession(TransactionIsolationLevel.READ_COMMITTED);
        verify(sessionManager).commit();
    }

    @Test
    public void testIsAllowedShouldReturnTrueWhenGivenCorrectChecker()
            throws Exception {
        // given
        String referer = "http://google.co.jp?test=test";
        String refererChecker = "google.co.jp,yahoo.co.jp";
        when(campaignMapper.findRefererCheckerBy(CAMPAIGN_ID)).thenReturn(refererChecker);

        // when
        boolean actual = underTest.isAllowed(referer, CAMPAIGN_ID);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsAllowedShouldReturnFalseWhenGivenIncorrectChecker()
            throws Exception {
        // given
        String referer = "http://interspace.co.jp?test=test";
        String refererChecker = "google.co.jp,yahoo.co.jp";
        when(campaignMapper.findRefererCheckerBy(CAMPAIGN_ID)).thenReturn(refererChecker);

        // when
        boolean actual = underTest.isAllowed(referer, CAMPAIGN_ID);

        // then
        assertFalse(actual);
    }

    private void assertFields(ConversionParameter actual, String transactionId,
            String paramName, String paramValue) {
        assertNotNull(actual);
        assertEquals(CAMPAIGN_ID, actual.getCampaignId());
        assertEquals(transactionId, actual.getTransactionId());
        assertEquals(paramName, actual.getParamName());
        assertEquals(paramValue, actual.getParamValue());
    }

    private void assertFields(ClickParameter actual, String internalTransactionId,
            String paramName, String paramValue) {
        assertNotNull(actual);
        assertEquals(CAMPAIGN_ID, actual.getCampaignId());
        assertEquals(internalTransactionId, actual.getInternalTransactionId());
        assertEquals(paramName, actual.getParamName());
        assertEquals(paramValue, actual.getParamValue());
    }

    private Conversion createConversion(long quantity) {
        return new Conversion(LOG_DATE_TIME, CREATIVE_ID, SITE_ID, CAMPAIGN_ID,
                DEVICE_TYPE, DEVICE_OS, IP_ADDRESS, REFERER, USER_AGENT,
                CLICK_DATE_TIME, CONVERSION_DATE_TIME, SESSION_ID, UUID, IDENTIFIER,
                RESULT_ID, quantity, PRICE, UNIT_PRICE,
                UNIT_PRICE.multiply(BigDecimal.valueOf(quantity)), DISCOUNT, PRODUCT_ID,
                CATEGORY_ID, POINTBACK_ID, CLICK_REFERER, CLICK_URL, CLICK_USER_AGENT,
                CONVERSION_PARAMETERS, CLICK_PARAMETERS, TRANSACTION_IDS, CUSTOMER_TYPE,
                LANGUAGE, CLICK_IP_ADDRESS, CURRENCY);
    }

    private InsertConversionRequest createInsertConversionRequest() {
        return new InsertConversionRequest(LOG_DATE_TIME, CREATIVE_ID, SITE_ID,
                CAMPAIGN_ID, DEVICE_TYPE, DEVICE_OS, IP_ADDRESS, REFERER, USER_AGENT,
                CLICK_DATE_TIME, CONVERSION_DATE_TIME, "transactionId",
                "internalTransactionId", SESSION_ID, 5, IDENTIFIER, RESULT_ID, PRICE,
                CURRENCY, PRICE, POINTBACK_ID, PRODUCT_ID, UUID, CATEGORY_ID, DISCOUNT,
                CLICK_REFERER, CLICK_URL, CLICK_USER_AGENT, CUSTOMER_TYPE, LANGUAGE,
                CLICK_IP_ADDRESS);
    }
}
