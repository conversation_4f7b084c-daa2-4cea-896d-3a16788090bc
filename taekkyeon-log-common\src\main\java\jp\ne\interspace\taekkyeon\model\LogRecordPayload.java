/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Payload for {@link LogRecord}.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor
public class LogRecordPayload {

    private final String receiptHandle;
    private final LogItem logItem;
}
