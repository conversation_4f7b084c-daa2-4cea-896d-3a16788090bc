/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.Currency;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.StringJoiner;
import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.inject.Inject;
import org.apache.commons.lang3.CharEncoding;

import jp.ne.interspace.taekkyeon.common.StringHelper;
import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;
import jp.ne.interspace.taekkyeon.model.DeviceOs;
import jp.ne.interspace.taekkyeon.model.DeviceType;
import jp.ne.interspace.taekkyeon.validator.LogValidator;

import static com.google.common.base.Strings.isNullOrEmpty;
import static java.lang.String.format;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.HYPHEN;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.UNDERSCORE;
import static jp.ne.interspace.taekkyeon.model.DeviceOs.ANDROID;
import static jp.ne.interspace.taekkyeon.model.DeviceOs.BLACKBERRY;
import static jp.ne.interspace.taekkyeon.model.DeviceOs.IPAD;
import static jp.ne.interspace.taekkyeon.model.DeviceOs.IPHONE;
import static jp.ne.interspace.taekkyeon.model.DeviceOs.IPOD;
import static jp.ne.interspace.taekkyeon.model.DeviceOs.LINUX;
import static jp.ne.interspace.taekkyeon.model.DeviceOs.MAC;
import static jp.ne.interspace.taekkyeon.model.DeviceOs.UNKNOWN;
import static jp.ne.interspace.taekkyeon.model.DeviceOs.WINDOWS;
import static jp.ne.interspace.taekkyeon.model.DeviceOs.WINDOWS_PHONE;

/**
 * Service for parsing log items.
 *
 * <AUTHOR> Varga
 */
@Singleton
public class LogItemParserService {

    private static final String FIELD_SEPARATOR = "#";
    private static final String VALUE_SEPARATOR = "=";

    protected static final String REQUEST_TIME = "REQUEST_TIME";
    private static final String BANNER_ID = "BANNER_ID";
    private static final String PARTNER_SITE_NO = "PARTNER_SITE_NO";
    private static final String MERCHANT_CAMPAIGN_NO = "MERCHANT_CAMPAIGN_NO";
    private static final String DEVICE_TYPE = "DEVICE_TYPE";
    private static final String IP_ADDRESS = "IP_ADDRESS";
    private static final String REFERER = "REFERER";
    private static final String USER_AGENT = "USER-AGENT";
    private static final String TRACKING_ID = "TRACKING_ID";
    private static final String PRODUCT_ID = "GOODS_ID";
    protected static final String CLICK_DATE = "CLICK_DATE";
    protected static final String CONVERSION_DATE = "SALES_DATE";
    private static final String IDENTIFIER = "IDENTIFIER";
    private static final String SESSION_ID = "SESSION_ID";
    private static final String REQUEST = "REQUEST";
    private static final String RESULT_ID = "RESULT_ID";
    private static final String VALUE = "VALUE";
    private static final String QUANTITY = "QUANTITY";
    private static final String UNIT_PRICE = "UNIT_PRICE";
    private static final String CATEGORY_ID = "CATEGORY_ID";
    private static final String POINTBACK_ID = "PBID";
    private static final String UUID = "UUID";
    private static final String DISCOUNT = "DISCOUNT";
    private static final String ORIGINAL_TOTAL_PRICE = "ORIGINAL_TOTAL_PRICE";
    private static final String CLICK_REFERER = "CLICK_REFERER";
    private static final String CLICK_URL = "CLICK_URL";
    private static final String CLICK_USER_AGENT = "CLICK_USER_AGENT";
    private static final String CUSTOMER_TYPE = "CUSTOMER_TYPE";
    private static final String COMPARE_KEY = "COMPARE_KEY";
    private static final String RK = "RK";
    private static final String TRANSACTION_AMOUNT = "TRANSACTION_AMOUNT";
    private static final String CONVERSION_PARAMETERS_PREFIX = "CV_PARAM_";
    private static final long DEFAULT_QUANTITY_VALUE = 1;
    private static final String LANGUAGE = "LANGUAGE";
    private static final String CLICK_IP_ADDRESS = "CLICK_IP_ADDRESS";
    private static final String CURRENCY = "CURRENCY";
    private static final int PARAMETER_NAME_MAX_BYTE_LENGTH = 128;
    private static final int PRODUCT_ID_MAX_BYTE_LENGTH = 256;
    private static final int CATEGORY_ID_MAX_BYTE_LENGTH = 256;
    private static final int IP_ADDRESS_MAX_BYTE_LENGTH = 256;
    private static final int REFERER_MAX_BYTE_LENGTH = 2048;
    private static final int CLICK_URL_MAX_BYTE_LENGTH = 2048;
    private static final int USER_AGENT_MAX_BYTE_LENGTH = 512;
    private static final int SESSION_ID_MAX_BYTE_LENGTH = 256;
    private static final int POINTBACK_ID_MAX_BYTE_LENGTH = 64;
    private static final int IDENTIFIER_MAX_BYTE_LENGTH = 256;
    private static final int CONVERSION_PARAMETER_MAX_BYTE_LENGTH = 2048;
    private static final int CLICK_PARAMETER_MAX_BYTE_LENGTH = 2048;
    private static final int CUSTOMER_TYPE_MAX_BYTE_LENGTH = 64;

    private static final int PRODUCT_REWARD_RESULT_ID = 3;
    private static final int CATEGORY_REWARD_RESULT_ID = 30;

    private static final int TRACKING_ID_MAX_LENGTH_VALUE = 40;

    private static final String NULL_STRING = "NULL";

    protected static final String PARAMETER_INVALID_MESSAGE_FORMAT = "The parameter [%s] is invalid";

    private static final ImmutableList<String> DEFAULT_KEYS = ImmutableList.of(BANNER_ID,
            PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, REQUEST_TIME, CLICK_DATE, IDENTIFIER,
            IP_ADDRESS, REFERER, USER_AGENT, REQUEST, RESULT_ID, VALUE, PRODUCT_ID,
            QUANTITY, UNIT_PRICE, DEVICE_TYPE, COMPARE_KEY, RK, SESSION_ID, CATEGORY_ID,
            DISCOUNT, ORIGINAL_TOTAL_PRICE, UUID, CONVERSION_DATE, TRANSACTION_AMOUNT,
            CUSTOMER_TYPE, CLICK_IP_ADDRESS, CURRENCY);

    private static final String UNKNOWN_VALUE = "unknown";

    @Inject
    private StringHelper stringHelper;

    @Inject
    private LogValidator logValidator;

    /**
     * Returns {@code rawLogItem} in a form of key-value pairs for each field inside it.
     *
     * @param rawLogItem
     *            the log item to parse
     * @return {@code rawLogItem} in a form of key-value pairs for each field inside it
     */
    public Map<String, String> parse(String rawLogItem) {
        Map<String, String> parsedLogItem = new HashMap<>();
        if (!Strings.isNullOrEmpty(rawLogItem)) {
            String[] rawFields = rawLogItem.split(FIELD_SEPARATOR);
            for (int i = 0; i < rawFields.length; i++) {
                String[] field = rawFields[i].split(VALUE_SEPARATOR);
                String fieldKey = field[0].toUpperCase();

                switch (field.length) {
                    case 1:
                        parsedLogItem.put(fieldKey, null);
                        break;
                    case 2:
                        parsedLogItem.put(fieldKey, field[1]);
                        break;
                    default:
                        StringJoiner joinedField = new StringJoiner(VALUE_SEPARATOR);
                        for (int j = 1; j < field.length; j++) {
                            joinedField.add(field[j]);
                        }
                        parsedLogItem.put(fieldKey, joinedField.toString());
                        break;
                }
            }
        }
        return parsedLogItem;
    }

    /**
     * Returns the parsed {@code creativeId} of the given log item.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the parsed {@code creativeId} of the given log item
     */
    public long getCreativeIdFrom(Map<String, String> parsedLogItem) {
        return Long.valueOf(parsedLogItem.get(BANNER_ID));
    }

    /**
     * Returns the parsed {@code siteId} of the given log item.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the parsed {@code siteId} of the given log item
     */
    public long getSiteIdFrom(Map<String, String> parsedLogItem) {
        return Long.valueOf(parsedLogItem.get(PARTNER_SITE_NO));
    }

    /**
     * Returns the parsed {@code campaignId} of the given log item.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the parsed {@code campaignId} of the given log item
     */
    public long getCampaignIdFrom(Map<String, String> parsedLogItem) {
        return Long.valueOf(parsedLogItem.get(MERCHANT_CAMPAIGN_NO));
    }

    /**
     * Returns the parsed {@code deviceType} of the given log item.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the parsed {@code deviceType} of the given log item
     */
    public DeviceType getDeviceTypeFrom(Map<String, String> parsedLogItem) {
        DeviceType deviceType = DeviceType.UNKNOWN;
        if (parsedLogItem.containsKey(DEVICE_TYPE)) {
            deviceType = DeviceType
                    .findBy(Integer.valueOf(parsedLogItem.get(DEVICE_TYPE)));
        }
        return deviceType;
    }

    /**
     * Returns the {@code ipAddress} of the given log item, truncated to the size of its
     * DB column.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the {@code ipAddress} of the given log item, truncated to the size of its
     *         DB column
     */
    public String getIpAddressFrom(Map<String, String> parsedLogItem) {
        return stringHelper.truncateToBytes(parsedLogItem.get(IP_ADDRESS),
                IP_ADDRESS_MAX_BYTE_LENGTH);
    }

    /**
     * Returns the {@code referer} of the given log item, truncated to the size of its DB
     * column.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the {@code referer} of the given log item, truncated to the size of its DB
     *         column
     */
    public String getRefererFrom(Map<String, String> parsedLogItem) {
        return stringHelper.truncateToBytes(parsedLogItem.get(REFERER),
                REFERER_MAX_BYTE_LENGTH);
    }

    /**
     * Returns the {@code userAgent} of the given log item, truncated to the size of its
     * DB column.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the {@code userAgent} of the given log item, truncated to the size of its
     *         DB column
     */
    public String getUserAgentFrom(Map<String, String> parsedLogItem) {
        return stringHelper.truncateToBytes(parsedLogItem.get(USER_AGENT),
                USER_AGENT_MAX_BYTE_LENGTH);
    }

    /**
     * Returns the {@code trackingId} of the given log item.
     *
     * @param parsedLogItem
     *           the given log item
     * @return the {@code trackingId} of the given log item
     */
    public String getTrackingIdFrom(Map<String, String> parsedLogItem) {
        return stringHelper.truncateToBytes(parsedLogItem.get(TRACKING_ID),
                TRACKING_ID_MAX_LENGTH_VALUE);
    }

    /**
     * Returns the {@code identifier} of the given log item.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the {@code identifier} of the given log item
     */
    public String getIdentifierFrom(Map<String, String> parsedLogItem) {
        return stringHelper.truncateToBytesFromFront(
                getValidatedValue(parsedLogItem, IDENTIFIER), IDENTIFIER_MAX_BYTE_LENGTH);
    }

    /**
     * Returns the {@code sessionId} of the given log item.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the {@code sessionId} of the given log item
     */
    public String getSessionIdFrom(Map<String, String> parsedLogItem) {
        String sessionId = getValidatedValue(parsedLogItem, SESSION_ID);
        logValidator.validateMaxByteCountOf(sessionId, SESSION_ID_MAX_BYTE_LENGTH,
                SESSION_ID);
        return sessionId;
    }

    /**
     * Returns the {@code request} of the given log item.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the {@code request} of the given log item
     */
    public String getRequestFrom(Map<String, String> parsedLogItem) {
        return getValidatedValue(parsedLogItem, REQUEST);
    }

    /**
     * Returns the {@code productId} of the given log item.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the {@code productId} of the given log item
     */
    public String getProductIdFrom(Map<String, String> parsedLogItem) {
        return stringHelper.truncateToBytes(parsedLogItem.get(PRODUCT_ID),
                PRODUCT_ID_MAX_BYTE_LENGTH);
    }

    /**
     * Returns the {@code quantity} of the given log item.
     *
     * @param parsedLogItem
     *            the given log item
     * @param resultId
     *            the given resultId
     * @return the {@code quantity} of the given log item
     */
    public long getQuantityFrom(Map<String, String> parsedLogItem, int resultId) {
        if (isProductOrCategoryReward(resultId)) {
            long quantity = Long.parseLong(parsedLogItem.get(QUANTITY));
            if (quantity >= DEFAULT_QUANTITY_VALUE) {
                return quantity;
            } else {
                throw new TaekkyeonException(
                        format(PARAMETER_INVALID_MESSAGE_FORMAT, QUANTITY));
            }
        } else {
            try {
                long quantity = Long.parseLong(parsedLogItem.get(QUANTITY));
                if (quantity >= DEFAULT_QUANTITY_VALUE) {
                    return quantity;
                } else {
                    return DEFAULT_QUANTITY_VALUE;
                }
            } catch (Exception ex) {
                return DEFAULT_QUANTITY_VALUE;
            }
        }
    }

    /**
     * Returns the parsed {@code unitPrice} of the given request,
     * if not contains {@code unitPrice}, return 0.
     *
     * @param parsedLogItem
     *            the given log item
     * @param resultId
     *            the given resultId
     * @return the parsed {@code price} of the given request
     *          if not contains {@code price}, return 0
     */
    public BigDecimal getUnitPriceFrom(Map<String, String> parsedLogItem, int resultId) {
        if (isProductOrCategoryReward(resultId)) {
            return new BigDecimal(parsedLogItem.get(UNIT_PRICE));
        } else {
            try {
                return new BigDecimal(parsedLogItem.get(UNIT_PRICE));
            } catch (Exception ex) {
                return BigDecimal.ZERO;
            }
        }
    }

    /**
     * Returns the {@code categoryId} of the given log item.
     *
     * @param parsedLogItem
     *            the given log item
     * @param resultId
     *            the given resultId
     * @return the {@code categoryId} of the given log item
     */
    public String getCategoryIdFrom(Map<String, String> parsedLogItem, int resultId) {
        String categoryId = parsedLogItem.get(CATEGORY_ID);
        if (isNullOrEmptyOrNullString(categoryId)
                && resultId == CATEGORY_REWARD_RESULT_ID) {
            throw new TaekkyeonException(
                    format(PARAMETER_INVALID_MESSAGE_FORMAT, CATEGORY_ID));
        }
        return stringHelper.truncateToBytes(categoryId, CATEGORY_ID_MAX_BYTE_LENGTH);
    }

    /**
     * Returns the customer type of the given log item.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the customer type of the given log item
     */
    public String getCustomerTypeFrom(Map<String, String> parsedLogItem) {
        String customerType = parsedLogItem.get(CUSTOMER_TYPE);
        return stringHelper.truncateToBytes(customerType, CUSTOMER_TYPE_MAX_BYTE_LENGTH);
    }

    /**
     * Returns the {@code pointbackId} of the given log item.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the {@code pointbackId} of the given log item
     */
    public String getPointbackIdFrom(Map<String, String> parsedLogItem) {
        String pointbackId = parsedLogItem.get(POINTBACK_ID);
        if (!isNullOrEmptyOrNullString(pointbackId)) {
            logValidator.validateMaxByteCountOf(pointbackId, POINTBACK_ID_MAX_BYTE_LENGTH,
                    POINTBACK_ID);
        }
        return pointbackId;
    }

    /**
     * Returns the {@code uuid} of the given log item.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the {@code uuid} of the given log item
     */
    public String getUuidFrom(Map<String, String> parsedLogItem) {
        return parsedLogItem.get(UUID);
    }

    /**
     * Returns the parsed {@code discount} of the given request,
     * if not contains {@code discount}, return 0.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the parsed {@code discount} of the given request
     *          if not contains {@code discount}, return 0
     */
    public BigDecimal getDiscountFrom(Map<String, String> parsedLogItem) {
        if (parsedLogItem.containsKey(DISCOUNT)) {
            return new BigDecimal(parsedLogItem.get(DISCOUNT));
        }
        return BigDecimal.ZERO;
    }

    /**
     * Returns the parsed {@code originalTotalPrice} of the given request,
     * if not contains {@code originalTotalPrice}, return 0.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the parsed {@code originalTotalPrice} of the given request
     *          if not contains {@code originalTotalPrice}, return 0
     */
    public BigDecimal getOriginalTotalPriceFrom(Map<String, String> parsedLogItem) {
        if (parsedLogItem.containsKey(ORIGINAL_TOTAL_PRICE)) {
            return new BigDecimal(parsedLogItem.get(ORIGINAL_TOTAL_PRICE));
        }
        return BigDecimal.ZERO;
    }

    /**
     * Returns the {@code clickReferer} of the given log item.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the {@code clickReferer} of the given log item
     */
    public String getClickRefererFrom(Map<String, String> parsedLogItem) {
        return decodeUrl(stringHelper.truncateToBytes(
                parsedLogItem.get(CLICK_REFERER), REFERER_MAX_BYTE_LENGTH));
    }

    /**
     * Returns the {@code clickUrl} of the given log item.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the {@code clickUrl} of the given log item
     */
    public String getClickUrlFrom(Map<String, String> parsedLogItem) {
        return decodeUrl(stringHelper.truncateToBytes(
                parsedLogItem.get(CLICK_URL), CLICK_URL_MAX_BYTE_LENGTH));
    }

    /**
     * Returns the {@code clickUserAgent} of the given log item.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the {@code clickUserAgent} of the given log item
     */
    public String getClickUserAgentFrom(Map<String, String> parsedLogItem) {
        return parsedLogItem.get(CLICK_USER_AGENT);
    }

    /**
     * Returns the {@code conversionParameters} of the given log item.
     *
     * @param parsedLogItem
     *          the given log item
     * @return the {@code conversionParameters} of the given log item
     */
    public Map<String, String> getConversionParametersFrom(
            Map<String, String> parsedLogItem) {
        Map<String, String> conversionParameters = new HashMap<>();
        for (Entry<String, String> entry : parsedLogItem.entrySet()) {
            if (!DEFAULT_KEYS.contains(entry.getKey())
                    && entry.getKey().contains(CONVERSION_PARAMETERS_PREFIX)) {
                String key = entry.getKey()
                        .replaceFirst(CONVERSION_PARAMETERS_PREFIX, EMPTY).toLowerCase();
                key = stringHelper.truncateToBytes(key, PARAMETER_NAME_MAX_BYTE_LENGTH);
                String value = stringHelper.truncateToBytes(entry.getValue(),
                        CONVERSION_PARAMETER_MAX_BYTE_LENGTH);
                conversionParameters.put(key, value);
            }
        }
        return conversionParameters;
    }

    /**
     * Returns the {@code clickParameters} of the given log item.
     *
     * @param parsedLogItem
     *          the given log item
     * @return the {@code clickParameters} of the given log item
     */
    public Map<String, String> getClickParametersFrom(Map<String, String> parsedLogItem) {
        Map<String, String> clickParameters = new HashMap<>();
        for (Entry<String, String> entry : parsedLogItem.entrySet()) {
            if (!DEFAULT_KEYS.contains(entry.getKey())
                    && !entry.getKey().contains(CONVERSION_PARAMETERS_PREFIX)
                    && !isNullOrEmptyOrNullString(entry.getValue())) {
                String key = entry.getKey().toLowerCase();
                key = stringHelper.truncateToBytes(key, PARAMETER_NAME_MAX_BYTE_LENGTH);
                String value = stringHelper.truncateToBytes(entry.getValue(),
                        CLICK_PARAMETER_MAX_BYTE_LENGTH);
                if (isClickUrl(key)) {
                    value = decodeUrl(value);
                }
                clickParameters.put(key, value);
            }
        }
        return clickParameters;
    }

    /**
     * Gets the {@link DeviceOs} from the {@code userAgent}.
     *
     * @param userAgent
     *            a User-Agent string
     * @return the {@link DeviceOs} from the {@code userAgent}
     */
    public DeviceOs getDeviceOsFrom(String userAgent) {
        DeviceOs os = UNKNOWN;
        if (isNullOrEmptyOrNullString(userAgent)) {
            return os;
        } else if (userAgent.toLowerCase().contains(ANDROID.getUserAgentFragment())) {
            os = ANDROID;
        } else if (userAgent.toLowerCase().contains(BLACKBERRY.getUserAgentFragment())) {
            os = BLACKBERRY;
        } else if (userAgent.toLowerCase().contains(IPHONE.getUserAgentFragment())) {
            os = IPHONE;
        } else if (userAgent.toLowerCase().contains(IPOD.getUserAgentFragment())) {
            os = IPOD;
        } else if (userAgent.toLowerCase().contains(IPAD.getUserAgentFragment())) {
            os = IPAD;
        } else if (userAgent.toLowerCase()
                .contains(WINDOWS_PHONE.getUserAgentFragment())) {
            os = WINDOWS_PHONE;
        } else if (userAgent.toLowerCase().contains(MAC.getUserAgentFragment())) {
            os = MAC;
        } else if (userAgent.toLowerCase().contains(WINDOWS.getUserAgentFragment())) {
            os = WINDOWS;
        } else if (userAgent.toLowerCase().contains(LINUX.getUserAgentFragment())) {
            os = LINUX;
        }
        return os;
    }

    /**
     * Returns the parsed {@code transactionAmount} of the given request,
     * if not contains {@code transactionAmount}, return {@code null}.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the parsed {@code transactionAmount} of the given request
     *          if not contains {@code transactionAmount}, return {@code null}
     */
    public BigDecimal getTransactionAmountFrom(Map<String, String> parsedLogItem) {
        String transactionAmount = parsedLogItem.get(TRANSACTION_AMOUNT);
        return isNullOrEmpty(transactionAmount) ? null
                : new BigDecimal(transactionAmount);
    }

    /**
     * Returns the {@code language} of the given log item.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the {@code language} of the given log item
     */
    public String getLanguageFrom(Map<String, String> parsedLogItem) {
        String language = parsedLogItem.get(LANGUAGE);
        if (isNullOrEmptyOrNullString(language)) {
            return UNKNOWN_VALUE;
        }
        String[] locals = language.replace(UNDERSCORE, HYPHEN).split(HYPHEN);
        return new Locale(locals[0]).getDisplayLanguage(Locale.ENGLISH);
    }

    /**
     * Returns the {@code clickIpAddress} of the given log item, truncated to the size
     * of its DB column.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the {@code clickIpAddress} of the given log item, truncated to the size
     *            of its DB column
     */
    public String getClickIpAddressFrom(Map<String, String> parsedLogItem) {
        return stringHelper.truncateToBytes(parsedLogItem.get(CLICK_IP_ADDRESS),
                IP_ADDRESS_MAX_BYTE_LENGTH);
    }

    /**
     * Returns the currency of the given log item.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the currency of the given log item
     */
    public String getCurrencyFrom(Map<String, String> parsedLogItem) {
        try {
            return Currency.getInstance(parsedLogItem.get(CURRENCY).toUpperCase())
                    .getCurrencyCode();
        } catch (Exception e) {
            return EMPTY;
        }
    }

    @VisibleForTesting
    String getValidatedValue(Map<String, String> parsedLogItem,
            String parameterName) {
        if (!isNullOrEmptyOrNullString(parsedLogItem.get(parameterName))) {
            return parsedLogItem.get(parameterName);
        }
        throw new TaekkyeonException(
                format(PARAMETER_INVALID_MESSAGE_FORMAT, parameterName));
    }

    @VisibleForTesting
    String decodeUrl(String url) {
        try {
            return URLDecoder.decode(url, CharEncoding.UTF_8);
        } catch (Exception e) {
            // do nothing.
        }
        return url;
    }

    private boolean isProductOrCategoryReward(int resultId) {
        return (resultId == PRODUCT_REWARD_RESULT_ID
                || resultId == CATEGORY_REWARD_RESULT_ID);
    }

    protected boolean isNullOrEmptyOrNullString(String string) {
        return (Strings.isNullOrEmpty(string) || NULL_STRING.equalsIgnoreCase(string));
    }

    private boolean isClickUrl(String key) {
        return CLICK_URL.equalsIgnoreCase(key) || CLICK_REFERER.equalsIgnoreCase(key);
    }
}
