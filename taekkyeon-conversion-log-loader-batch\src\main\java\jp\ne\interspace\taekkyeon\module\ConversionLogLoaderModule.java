/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import java.time.Duration;

import javax.inject.Singleton;

import com.google.common.collect.ImmutableTable;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;

import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import jp.ne.interspace.taekkyeon.factory.ConversionFactory;
import jp.ne.interspace.taekkyeon.factory.LogItemFactory;
import jp.ne.interspace.taekkyeon.loader.ConversionLoader;
import jp.ne.interspace.taekkyeon.loader.LogItemLoader;
import jp.ne.interspace.taekkyeon.service.BudgetCapCalculatorService;
import jp.ne.interspace.taekkyeon.service.RedisProductCategoryBudgetCapCalculatorService;
import jp.ne.interspace.taekkyeon.service.RedisTransactionBudgetCapCalculatorService;

import static jp.ne.interspace.taekkyeon.module.Country.INDONESIA;
import static jp.ne.interspace.taekkyeon.module.Country.THAILAND;
import static jp.ne.interspace.taekkyeon.module.Country.VIETNAM;
import static jp.ne.interspace.taekkyeon.module.Country.getCurrentCountry;
import static jp.ne.interspace.taekkyeon.module.Environment.DEV;
import static jp.ne.interspace.taekkyeon.module.Environment.PRODUCTION;
import static jp.ne.interspace.taekkyeon.module.Environment.STAGING;
import static jp.ne.interspace.taekkyeon.module.Environment.getCurrentEnvironment;

/**
 * Guice module for the conversion log loader batch.
 *
 * <AUTHOR> Shin
 */
public class ConversionLogLoaderModule extends AbstractModule {

    private static final ImmutableTable<Country, Environment, String> REDIS_CLUSTERS = new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, DEV, "localhost")
            .put(INDONESIA, STAGING,
                    "production-vn-conversion-loader.kb3f8j.ng.0001.apse1.cache.amazonaws.com")
            .put(INDONESIA, PRODUCTION,
                    "production-vn-conversion-loader.kb3f8j.ng.0001.apse1.cache.amazonaws.com")
            .put(THAILAND, DEV, "localhost")
            .put(THAILAND, STAGING,
                    "production-vn-conversion-loader.kb3f8j.ng.0001.apse1.cache.amazonaws.com")
            .put(THAILAND, PRODUCTION,
                    "production-vn-conversion-loader.kb3f8j.ng.0001.apse1.cache.amazonaws.com")
            .put(VIETNAM, DEV, "localhost")
            .put(VIETNAM, STAGING,
                    "production-vn-conversion-loader.kb3f8j.ng.0001.apse1.cache.amazonaws.com")
            .put(VIETNAM, PRODUCTION,
                    "production-vn-conversion-loader.kb3f8j.ng.0001.apse1.cache.amazonaws.com")
            .build();

    @Override
    protected void configure() {
        install(new CommonLogLoaderModuleForNewRefactoring());

        bind(LogItemFactory.class).to(ConversionFactory.class);
        bind(LogItemLoader.class).to(ConversionLoader.class);

        bind(BudgetCapCalculatorService.class)
                .annotatedWith(TransactionBudgetCapCalculatorServiceBinding.class)
                .to(RedisTransactionBudgetCapCalculatorService.class);
        bind(BudgetCapCalculatorService.class)
                .annotatedWith(ProductCategoryBudgetCapCalculatorServiceBinding.class)
                .to(RedisProductCategoryBudgetCapCalculatorService.class);
        bind(Integer.class).annotatedWith(CustomBatchSizeBinding.class).toInstance(1);
    }

    @Provides @Singleton
    private JedisPool provideJedisPool() {
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxTotal(128);
        poolConfig.setMaxIdle(128);
        poolConfig.setMinIdle(16);
        poolConfig.setTestOnBorrow(true);
        poolConfig.setTestOnReturn(true);
        poolConfig.setTestWhileIdle(true);
        poolConfig.setMinEvictableIdleDuration(Duration.ofSeconds(60));
        poolConfig.setTimeBetweenEvictionRuns(Duration.ofSeconds(30));
        poolConfig.setNumTestsPerEvictionRun(3);
        poolConfig.setBlockWhenExhausted(true);

        String redisCluster = "redis://"
                + REDIS_CLUSTERS.get(getCurrentCountry(), getCurrentEnvironment())
                + ":6379";

        return new JedisPool(poolConfig, redisCluster);
    }
}
