SET DATABASE SQL SYNTAX ORA TRUE;

INSERT INTO AFFILIATION_RANK_HISTORY (PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, TARGET_MONTH, RANK)
VALUES (1, 1, TO_DATE('201701', '<PERSON><PERSON><PERSON><PERSON><PERSON>'), 5);
INSERT INTO AFFILIATION_RANK_HISTORY (PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, TARGET_MONTH, RANK)
VALUES (1, 2, TO_DATE('201701', 'YYYYMM'), 6);

INSERT INTO MERCHANT_CAMPAIGN_SETTING (CA<PERSON>A<PERSON>N_NO, VERIFY_CUT_FLAG, VERIFY_CUT_TARGET, VERIFY_CUT_CONDITION)
VALUES (1, 1, 1, 1);
INSERT INTO MERCHANT_CAMPAIGN_SETTING (CAMPAIG<PERSON>_<PERSON>, VERIFY_CUT_FLAG, VERIFY_CUT_TARGET)
VALUES (2, 1, 0);

INSERT INTO MERCHANT_CAMPAIGN (CA<PERSON><PERSON><PERSON><PERSON>_<PERSON>, ACCOUNT_NO, CAMPAIGN_STATE_ID, CAMPAIGN_NAME, URL, IMAGE_URL, DESCRIPTION, CATEGORY1, CATEGORY2, CATEGORY3, AUTO_AFF_LIMITATION_OPTION, AUTO_AFF_LIMITATION_DIVISION, GET_PARAMETER_FLAG, SELF_CONVERSION_FLAG, POINTBACK_PERMISSION, CAMPAIGN_START_DATE, CAMPAIGN_END_DATE, CAMPAIGN_TYPE, CURRENCY, REFERER_CHECK, OVERLAP_FLG)
VALUES(1, 1, 1, 'campaign1', 'http://test1.ne.jp', 'http://test11.ne.jp', 'This is test', 0, 0, 0, 0, 0, 0, 1, 0, TO_DATE('2016/03/05', 'YYYY/MM/DD'), TO_DATE('2016/07/03', 'YYYY/MM/DD'), 0, 'USD', 'google.co.jp,interspace.co.jp', 1);
INSERT INTO MERCHANT_CAMPAIGN (CAMPAIGN_NO, ACCOUNT_NO, CAMPAIGN_STATE_ID, CAMPAIGN_NAME, URL, IMAGE_URL, DESCRIPTION, CATEGORY1, CATEGORY2, CATEGORY3, AUTO_AFF_LIMITATION_OPTION, AUTO_AFF_LIMITATION_DIVISION, GET_PARAMETER_FLAG, SELF_CONVERSION_FLAG, POINTBACK_PERMISSION, CAMPAIGN_START_DATE, CAMPAIGN_END_DATE, CAMPAIGN_TYPE, CURRENCY, REFERER_CHECK)
VALUES(2, 1, 1, 'campaign2', 'http://test2.ne.jp', 'http://test22.ne.jp', 'This is test', 0, 0, 0, 0, 0, 0, 1, 0, TO_DATE('2016/03/05', 'YYYY/MM/DD'), TO_DATE('2016/07/03', 'YYYY/MM/DD'), 0, 'IDR', 'google.co.jp,interspace.co.jp');
INSERT INTO MERCHANT_CAMPAIGN (CAMPAIGN_NO, ACCOUNT_NO, CAMPAIGN_STATE_ID, CAMPAIGN_NAME, URL, IMAGE_URL, DESCRIPTION, CATEGORY1, CATEGORY2, CATEGORY3, AUTO_AFF_LIMITATION_OPTION, AUTO_AFF_LIMITATION_DIVISION, GET_PARAMETER_FLAG, SELF_CONVERSION_FLAG, POINTBACK_PERMISSION, CAMPAIGN_START_DATE, CAMPAIGN_END_DATE, CAMPAIGN_TYPE, CURRENCY, REFERER_CHECK)
VALUES(3, 1, 1, 'campaign3', 'http://test3.ne.jp', 'http://test33.ne.jp', 'This is test', 0, 0, 0, 0, 0, 0, 1, 0, TO_DATE('2016/03/05', 'YYYY/MM/DD'), TO_DATE('2016/07/03', 'YYYY/MM/DD'), 0, 'IDR', 'google.co.jp,interspace.co.jp');

INSERT INTO CURRENCY_EXCHANGE_RATE_HISTORY (CURRENCY, TARGET_MONTH, RATE, QUOTE_CURRENCY)
VALUES ('USD', TO_DATE('201701', 'YYYYMM'), 1.2, 'IDR');
INSERT INTO CURRENCY_EXCHANGE_RATE_HISTORY (CURRENCY, TARGET_MONTH, RATE, QUOTE_CURRENCY)
VALUES ('JPY', TO_DATE('202106', 'YYYYMM'), 123.45, 'IDR');

INSERT INTO COUNTRY (CODE, CURRENCY)
VALUES ('ID', 'IDR');

INSERT INTO MERCHANT_ACCOUNT (ACCOUNT_NO, MERCHANT_TYPE_ID, ACCOUNT_STATE, COUNTRY_CODE)
VALUES (1, 1, 1, 'ID');

INSERT INTO RESULT_TARGET_SETTING (MERCHANT_CAMPAIGN_NO, RESULT_ID, RESULT_NAME, REWARD_TYPE, USE_FLAG, PARTNER_INVISIBLE_FLAG)
VALUES (1, 1, 'TEST1', 1, 1, 0);
INSERT INTO RESULT_TARGET_SETTING (MERCHANT_CAMPAIGN_NO, RESULT_ID, RESULT_NAME, REWARD_TYPE, USE_FLAG, PARTNER_INVISIBLE_FLAG)
VALUES (1, 2, 'TEST2', 1, 1, 0);
INSERT INTO RESULT_TARGET_SETTING (MERCHANT_CAMPAIGN_NO, RESULT_ID, RESULT_NAME, REWARD_TYPE, USE_FLAG, PARTNER_INVISIBLE_FLAG)
VALUES (1, 3, 'TEST3', 1, 1, 0);
INSERT INTO RESULT_TARGET_SETTING (MERCHANT_CAMPAIGN_NO, RESULT_ID, RESULT_NAME, REWARD_TYPE, USE_FLAG, PARTNER_INVISIBLE_FLAG)
VALUES (1, 30, 'TEST30', 1, 1, 0);
INSERT INTO RESULT_TARGET_SETTING (MERCHANT_CAMPAIGN_NO, RESULT_ID, RESULT_NAME, REWARD_TYPE, USE_FLAG, PARTNER_INVISIBLE_FLAG)
VALUES (3, 1, 'TEST31', 1, 1, 0);
INSERT INTO RESULT_TARGET_SETTING (MERCHANT_CAMPAIGN_NO, RESULT_ID, RESULT_NAME, REWARD_TYPE, USE_FLAG, PARTNER_INVISIBLE_FLAG)
VALUES (3, 3, 'TEST33', 1, 1, 0);
INSERT INTO RESULT_TARGET_SETTING (MERCHANT_CAMPAIGN_NO, RESULT_ID, RESULT_NAME, REWARD_TYPE, USE_FLAG, PARTNER_INVISIBLE_FLAG)
VALUES (10, 1, 'TEST10', 1, 1, 0);
INSERT INTO RESULT_TARGET_SETTING (MERCHANT_CAMPAIGN_NO, RESULT_ID, RESULT_NAME, REWARD_TYPE, USE_FLAG, PARTNER_INVISIBLE_FLAG)
VALUES (11, 2, 'TEST11', 1, 1, 0);

INSERT INTO POSTBACK_URL VALUES (301, 0, 'siteWithPostbackUrl', 1, 'TEST', NULL, NULL, NULL);
INSERT INTO POSTBACK_URL VALUES (302, 0, 'siteWithoutPostbackUrl', 1, 'TEST', NULL, NULL, NULL);
INSERT INTO POSTBACK_URL VALUES (303, 1024, 'siteWithPostbackUrlTargetCreative', 1, 'TEST', NULL, NULL, NULL);

INSERT INTO PARTNER_SITE VALUES (301,186,'siteWithPostback','siteWithPostbackUrl','siteWithPostbackDescription',3,3,0,0,0,0,0,1,'TEST',NULL,NULL,NULL);
INSERT INTO PARTNER_SITE VALUES (302,186,'siteWithPostbackDisabled','siteWithPostbackDisabledUrl','siteWithPostbackDisabledDescription',3,3,0,0,0,0,0,0,'TEST',NULL,NULL,NULL);
INSERT INTO PARTNER_SITE VALUES (303,186,'siteWithPostbackTargetCreative','siteWithPostbackUrl','siteWithPostbackDescription',3,3,0,0,0,0,0,0,'TEST',NULL,NULL,NULL);
INSERT INTO PARTNER_SITE VALUES (304,186,'siteWithoutPostback','siteWithoutPostbackUrl','siteWithoutPostbackDescription',3,3,0,0,0,0,0,1,'TEST',NULL,NULL,NULL);
INSERT INTO PARTNER_SITE VALUES (101,9,'sitePaidPublisherAgencyCommission','','',3,3,0,0,0,0,0,1,'TEST',NULL,NULL,NULL);
INSERT INTO PARTNER_SITE VALUES (102,10,'siteNotPaidDueToWithoutPublisherAgencyCommissionPolicy','','',3,3,0,0,0,0,0,1,'TEST',NULL,NULL,NULL);
INSERT INTO PARTNER_SITE VALUES (5, 11,'testSite5','','',3,3,0,0,0,0,0,1,'TEST',NULL,NULL,NULL);

INSERT INTO CLICK_PARAMETERS (MERCHANT_CAMPAIGN_NO, INTERNAL_TRANSACTION_ID, PARAM_NAME, PARAM_VALUE)
VALUES (2, 'internalTransactionId1', 'paramName1', 'paramValue1');
