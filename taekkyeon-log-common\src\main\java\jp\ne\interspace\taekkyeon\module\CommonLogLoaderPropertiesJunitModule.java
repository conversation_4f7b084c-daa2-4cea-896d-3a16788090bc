/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import com.google.inject.AbstractModule;

import static jp.ne.interspace.taekkyeon.module.LogType.IMPRESSION;

/**
 * Guice module for common log loading Junit tests.
 *
 * <AUTHOR>
 */
public class CommonLogLoaderPropertiesJunitModule extends AbstractModule {

    private static final String LOG_TYPE_TEST_VM_ARGUMENT = "logType";
    private static final String MAX_EXECUTION_SECONDS_TEST_VM_ARGUMENT = "maxExecutionSeconds";
    private static final String VISIBILITY_TIMEOUT_SECONDS_VM_ARGUMENT = "visibilityTimeoutSeconds";
    private static final String WAIT_TIME_SECONDS_VM_ARGUMENT = "waitTimeSeconds";

    @Override
    protected void configure() {
        setTestSystemPropertyValues();
    }

    private void setTestSystemPropertyValues() {
        System.setProperty(LOG_TYPE_TEST_VM_ARGUMENT, IMPRESSION.getCode());
        System.setProperty(MAX_EXECUTION_SECONDS_TEST_VM_ARGUMENT, "42");
        System.setProperty(VISIBILITY_TIMEOUT_SECONDS_VM_ARGUMENT, "60");
        System.setProperty(WAIT_TIME_SECONDS_VM_ARGUMENT, "2");
    }
}
