/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import static java.math.BigDecimal.ZERO;
import static jp.ne.interspace.taekkyeon.model.CommissionType.NET;
import static jp.ne.interspace.taekkyeon.model.ConversionStatus.PENDING;
import static jp.ne.interspace.taekkyeon.model.PostbackStatus.NOT_NEEDED;
import static jp.ne.interspace.taekkyeon.model.RewardType.CPA_FIXED;

/**
 * DTO for holding the data of a conversion log.
 *
 * <AUTHOR> Shin
 */
@EqualsAndHashCode(callSuper = true) @Getter @Setter @ToString(callSuper = true)
public class ConversionInsertion extends ConversionBase {

    private final Long creativeId;
    private final Long campaignId;
    private final LocalDateTime clickTime;
    private final LocalDateTime conversionTime;
    private final LocalDateTime logTime;
    private final String transactionId;
    private final String sessionId;
    private final Long siteId;
    private final Integer rank;
    private final String verify;
    private final Integer resultId;
    private final String productId;
    private final ConversionStatus conversionStatus;
    private final BigDecimal totalPrice;
    private final BigDecimal originalCurrencyTotalPrice;
    private final String originalCurrency;
    private final String ipAddress;
    private final String referer;
    private final String userAgent;
    private final BigDecimal defaultConversionCount;
    private final BigDecimal defaultPrice;
    private final DeviceType deviceType;
    private final String pointbackId;
    private final String uuid;
    private final DeviceOs deviceOs;
    private final String clickReferer;
    private final String clickUrl;
    private final String clickUserAgent;
    private RewardType rewardType = CPA_FIXED;
    private CommissionType commissionType = NET;
    private PostbackStatus postbackStatus = NOT_NEEDED;
    private final String publisherCountryCode;
    private final boolean redshiftSyncRequired = true;
    private BigDecimal publisherRewardInUsd = ZERO;
    private BigDecimal transactionAmountInUsd = ZERO;
    private BigDecimal atCommissionInUsd = ZERO;
    private BigDecimal agentCommissionInUsd = ZERO;
    private BigDecimal publisherAgentCommissionInUsd = ZERO;
    private BigDecimal discountInUsd = ZERO;
    private BigDecimal unitPriceInUsd = ZERO;
    private final String language;
    private final String clickIpAddress;
    private final DuplicationCut duplicationCut;

    /**
     * Constructor to be used by the relevant MyBatis mapper. Cannot be auto-generated
     * because of the {@code super()} call.
     */
    public ConversionInsertion(InsertConversionRequest request,
            PublisherData publisherData, String merchantCountryCode,
            DuplicationCut duplicationCut) {
        super(new BigDecimal("1"), request.getDefaultPrice(),
                publisherData.getPublisherAgencyPolicy(),
                request.getInternalTransactionId(), request.getCategoryId(),
                merchantCountryCode, request.getDefaultPrice());
        this.creativeId = request.getCreativeId();
        this.campaignId = request.getCampaignId();
        this.clickTime = request.getClickDate();
        this.conversionTime = request.getConversionDate();
        this.logTime = request.getLogDate();
        this.transactionId = request.getTransactionId();
        this.sessionId = request.getSessionId();
        this.siteId = request.getSiteId();
        this.rank = request.getRank();
        this.verify = request.getIdentifier();
        this.resultId = request.getResultId();
        this.productId = request.getProductId();
        this.conversionStatus = PENDING;
        this.totalPrice = request.getDefaultPrice();
        this.originalCurrencyTotalPrice = request.getOriginalCurrencyTotalPrice();
        this.originalCurrency = request.getOriginalCurrency();
        this.ipAddress = request.getIpAddress();
        this.referer = request.getReferer();
        this.userAgent = request.getUserAgent();
        this.defaultConversionCount = new BigDecimal("1");
        this.defaultPrice = request.getDefaultPrice();
        this.deviceType = request.getDeviceType();
        this.pointbackId = request.getPointbackId();
        this.uuid = request.getUuid();
        this.deviceOs = request.getDeviceOs();
        this.clickReferer = request.getClickReferer();
        this.clickUrl = request.getClickUrl();
        this.clickUserAgent = request.getClickUserAgent();
        this.publisherCountryCode = publisherData.getCountryCode();
        this.language = request.getLanguage();
        this.clickIpAddress = request.getClickIpAddress();
        this.duplicationCut = duplicationCut;
        this.setCustomerType(request.getCustomerType());
        this.setDiscount(request.getDiscount());
    }
}
