/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.time.LocalDateTime;

import lombok.Getter;
import lombok.ToString;

/**
 * DTO for holding the data of a click.
 *
 * <AUTHOR>
 */
@Getter @ToString(callSuper = true)
public class Click extends LogItem {

    public static final Click DEFAULT_CLICK = new Click(null, 0, 0, 0, null, null, null,
            null, null, null, null, null, null);

    private final String trackingId;
    private final String productId;
    private final String optimizerUuid;
    private final String language;

    /**
     * Constructor to be used by the relevant MyBatis mapper. Cannot be auto-generated
     * because of the {@code super()} call.
     */
    public Click(LocalDateTime logDate, long creativeId, long siteId, long campaignId,
            DeviceType deviceType, DeviceOs deviceOs, String ipAddress, String referer,
            String userAgent, String trackingId,
            String productId, String optimizerUuid, String language) {
        super(logDate, creativeId, siteId, campaignId, deviceType, deviceOs, ipAddress,
                referer, userAgent);

        this.trackingId = trackingId;
        this.productId = productId;
        this.optimizerUuid = optimizerUuid;
        this.language = language;
    }

}
