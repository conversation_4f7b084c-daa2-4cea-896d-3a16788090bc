/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.Map;
import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;
import jp.ne.interspace.taekkyeon.util.DateUtils;

import static java.lang.String.format;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DATE_TIME_FORMATTER;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DATE_TIME_FORMATTER_YYYY_SLASH_MM_SLASH_DDTHHMMSS;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DATE_TIME_FORMATTER_YYYY_SLASH_MM_SLASH_DD_HHMMSS;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.ISO_8061_DATE_TIME_FORMATTER;

/**
 * Service for parsing log items in advanced.
 *
 * <AUTHOR> Tran
 */
@Singleton
public class AdvancedLogItemParserService extends LogItemParserService {

    @Inject
    private LogItemParserService logItemParserService;

    @Inject
    private MerchantAccountService merchantAccountService;

    @Inject
    private CountryService countryService;

    @Inject
    private DateUtils dateUtils;

    /**
     * Returns the parsed {@code logDate} of the given log item.
     *
     * @param parsedLogItem
     *            the given log item
     * @return the parsed {@code logDate} of the given log item
     */
    public LocalDateTime getLogDateFrom(Map<String, String> parsedLogItem) {
        return createDateFrom(logItemParserService.getCampaignIdFrom(parsedLogItem),
                LocalDateTime.parse(parsedLogItem.get(REQUEST_TIME),
                        DATE_TIME_FORMATTER));
    }

    /**
     * Returns the parsed {@code clickDate} of the given log item.
     * if not contains {@code clickDate} key, return null
     *
     * @param parsedLogItem
     *            the given log item
     * @return the parsed {@code clickDate} of the given log item
     *          if not contains {@code clickDate} key, return null
     */
    public LocalDateTime getClickDateFrom(Map<String, String> parsedLogItem) {
        if (parsedLogItem.containsKey(CLICK_DATE)) {
            return createDateFrom(logItemParserService.getCampaignIdFrom(parsedLogItem),
                    LocalDateTime.parse(parsedLogItem.get(CLICK_DATE),
                            DATE_TIME_FORMATTER));
        }
        throw new TaekkyeonException(
                format(PARAMETER_INVALID_MESSAGE_FORMAT, CLICK_DATE));
    }

    /**
     * Returns the parsed {@code conversionDate} of the given log item.
     * if not contains {@code conversionDate} key, return null
     *
     * @param parsedLogItem
     *            the given log item
     * @return the parsed {@code conversionDate} of the given log item
     *          if not contains {@code conversionDate} key, return null
     */
    public LocalDateTime getConversionDateFrom(Map<String, String> parsedLogItem) {
        String conversionDate = parsedLogItem.get(CONVERSION_DATE);
        if (!isNullOrEmptyOrNullString(conversionDate)) {
            DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                    .appendOptional(DATE_TIME_FORMATTER)
                    .appendOptional(ISO_8061_DATE_TIME_FORMATTER)
                    .appendOptional(DATE_TIME_FORMATTER_YYYY_SLASH_MM_SLASH_DD_HHMMSS)
                    .appendOptional(DATE_TIME_FORMATTER_YYYY_SLASH_MM_SLASH_DDTHHMMSS)
                    .toFormatter();
            return LocalDateTime.parse(conversionDate, formatter);
        }
        return getLogDateFrom(parsedLogItem);
    }

    @VisibleForTesting
    LocalDateTime createDateFrom(long campaignId, LocalDateTime dateTime) {
        String countryCode = merchantAccountService.findCountryCodeBy(campaignId);
        String zoneId = countryService.findZoneIdBy(countryCode);
        return dateUtils.convertByTimeZone(dateTime, zoneId).toLocalDateTime();
    }
}
