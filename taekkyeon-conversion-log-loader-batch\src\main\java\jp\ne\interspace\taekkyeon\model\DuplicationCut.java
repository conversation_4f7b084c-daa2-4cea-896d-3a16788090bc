/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

/**
 * DTO for holding duplication cut information.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter @EqualsAndHashCode @ToString
public class DuplicationCut {

    private final boolean overlapFlag;
    private final boolean verifyCutFlag;
    private final Integer verifyCutTarget;
    private final Integer verifyCutCondition;
}
