/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import org.apache.ibatis.annotations.Insert;

import jp.ne.interspace.taekkyeon.model.ClickParameter;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * Mybat<PERSON> mapper for handling click parameters.
 *
 * <AUTHOR> Shin
 */
public interface ClickParametersMapper {

    /**
        MERGE INTO
            click_parameters cp
        USING (
            SELECT
                #{campaignId} campaign_id,
                #{internalTransactionId} internal_transaction_id,
                #{paramName} param_name,
                #{paramValue, jdbcType=VARCHAR} param_value
            FROM
                dual
        ) temp
        ON (
                cp.merchant_campaign_no = temp.campaign_id
            AND
                cp.internal_transaction_id = temp.internal_transaction_id
            AND
                cp.param_name = temp.param_name
        )
        WHEN NOT MATCHED THEN
            INSERT (
                merchant_campaign_no,
                internal_transaction_id,
                param_name,
                param_value,
                created_by,
                created_on)
            VALUES (
                temp.campaign_id,
                temp.internal_transaction_id,
                temp.param_name,
                temp.param_value,
                'ConversionLoaderBatch',
                SYSDATE
            )
     */
    @Multiline String INSERT_CLICK_PARAMETERS = "";

    /**
     * Inserts the click parameter by the given {@link ClickParameter}.
     *
     * @param clickParameter
     *            the click parameter of conversion
     * @return if data is found, the number of inserted rows, otherwise zero
     * @see #INSERT_CLICK_PARAMETERS
     */
    @Insert(INSERT_CLICK_PARAMETERS)
    int insert(ClickParameter clickParameter);
}
