/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

/**
 * Unit test for {@link ConversionRequestParserService}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class ConversionRequestParserServiceTest {

    private static final String RAW_REQUEST = "identifier:identifier&result_id";
    private static final String IDENTIFIER = "IDENTIFIER";
    private static final String RESULT_ID = "RESULT_ID";
    private static final String VALUE = "VALUE";
    private static final String BANNER_ID = "BANNER_ID";
    private static final String NUMBER_VALUE = "1";

    @InjectMocks
    private ConversionRequestParserService underTest;

    @Test
    public void testParseRequestShouldReturnAllFieldsCorrectlyWhenRawLogItemIsNotEmpty() {
        // when
        Map<String, String> actual = underTest.parseRequest(RAW_REQUEST);

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertEquals("identifier", actual.get(IDENTIFIER));
        assertTrue(actual.containsKey(RESULT_ID));
        assertNull(actual.get(BANNER_ID));
    }

    @Test
    public void testGetResultIdFromShouldReturnResultIdWhenGivenResultIdIsPositive() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(RESULT_ID, NUMBER_VALUE);

        // when
        int actual = underTest.getResultIdFrom(parsedLogItem);

        // then
        assertEquals(1, actual);
    }

    @Test
    public void testGetResultIdFromShouldReturnResultIdWhenGivenResultIdIsZero() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(RESULT_ID, "0");

        // when
        int actual = underTest.getResultIdFrom(parsedLogItem);

        // then
        assertEquals(0, actual);
    }

    @Test(expected = NumberFormatException.class)
    public void testGetResultIdFromShouldThrowExceptionWhenGivenEmptyData() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();

        // when
        underTest.getResultIdFrom(parsedLogItem);
    }

    @Test(expected = NumberFormatException.class)
    public void testGetResultIdFromShouldThrowExceptionWhenGivenResultIdIsNull() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(RESULT_ID, null);

        // when
        underTest.getResultIdFrom(parsedLogItem);
    }

    @Test(expected = NumberFormatException.class)
    public void testGetResultIdFromShouldThrowExceptionWhenGivenResultIdIsEmpty() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(RESULT_ID, "");

        // when
        underTest.getResultIdFrom(parsedLogItem);
    }

    @Test(expected = TaekkyeonException.class)
    public void testGetResultIdFromShouldThrowExceptionWhenGivenResultIdIsNegative() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(RESULT_ID, "-1");

        // when
        underTest.getResultIdFrom(parsedLogItem);
    }

    @Test
    public void testGetPriceFromShouldReturnPriceWhenCalled() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(VALUE, NUMBER_VALUE);

        // when
        BigDecimal actual = underTest.getPriceFrom(parsedLogItem);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.intValue());
    }

    @Test
    public void testGetPriceFromShouldReturnZeroWhenGivenEmptyData() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();

        // when
        BigDecimal actual = underTest.getPriceFrom(parsedLogItem);

        // then
        assertNotNull(actual);
        assertEquals(0, actual.intValue());
    }

    @Test
    public void testGetPriceFromShouldReturnZeroWhenGivenValueIsNull() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(VALUE, null);

        // when
        BigDecimal actual = underTest.getPriceFrom(parsedLogItem);

        // then
        assertNotNull(actual);
        assertEquals(0, actual.intValue());
    }

    @Test
    public void testGetPriceFromShouldReturnZeroWhenGivenValueIsEmpty() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(VALUE, "");

        // when
        BigDecimal actual = underTest.getPriceFrom(parsedLogItem);

        // then
        assertNotNull(actual);
        assertEquals(0, actual.intValue());
    }

    @Test
    public void testGetPriceFromShouldReturnZeroWhenGivenValueIsNotNumber() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(VALUE, "N%2FA");

        // when
        BigDecimal actual = underTest.getPriceFrom(parsedLogItem);

        // then
        assertNotNull(actual);
        assertEquals(0, actual.intValue());
    }
}
