/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.ClickParameter;

import static org.junit.Assert.assertEquals;

/**
 * Integration test for {@link ClickParametersMapper}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class ClickParametersMapperTest {

    @Inject
    private ClickParametersMapper underTest;

    @Test
    public void testInsertShouldReturnOneWhenDataIsNotFound() {
        // given
        ClickParameter parameter = new ClickParameter(1, "internalTransactionId",
                "paramName", "paramValue");

        // when
        int actual = underTest.insert(parameter);

        // then
        assertEquals(1, actual);
    }

    @Test
    public void testInsertShouldReturnZeroWhenDataIsFound() {
        // given
        ClickParameter parameter = new ClickParameter(2, "internalTransactionId1",
                "paramName1", "paramValue1");

        // when
        int actual = underTest.insert(parameter);

        // then
        assertEquals(0, actual);
    }
}
