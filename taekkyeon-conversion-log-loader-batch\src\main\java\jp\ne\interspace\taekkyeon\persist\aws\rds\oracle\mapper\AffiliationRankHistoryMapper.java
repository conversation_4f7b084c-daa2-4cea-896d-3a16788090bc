/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.time.LocalDateTime;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * My<PERSON><PERSON> mapper for handling affiliation rank history data.
 *
 * <AUTHOR> Shin
 */
public interface AffiliationRankHistoryMapper {

    /**
        SELECT
            rank
        FROM
            affiliation_rank_history
        WHERE
            partner_site_no = #{siteId}
        AND
            merchant_campaign_no = #{campaignId}
        AND
            target_month <= TRUNC(#{conversionDate}, 'DD')
        ORDER BY
            target_month DESC
        FETCH FIRST 1 ROW ONLY
     */
    @Multiline String SELECT_RANK = "";

    /**
     * Returns the rank of the campaign found by the given parameters.
     *
     * @param campaignId
     *          the identifiers of the given campaign
     * @param siteId
     *          the identifiers of the given site
     * @param conversionDate
     *          date of conversion
     * @return the rank by the given parameters
     * @see #SELECT_RANK
     */
    @Select(SELECT_RANK)
    Integer findRankBy(@Param("campaignId") long campaignId, @Param("siteId") long siteId,
            @Param("conversionDate") LocalDateTime conversionDate);
}
