/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.inject.Inject;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.params.SetParams;

import jp.ne.interspace.taekkyeon.model.ConversionInsertion;
import jp.ne.interspace.taekkyeon.model.ConversionRewards;
import jp.ne.interspace.taekkyeon.model.DuplicationCut;
import jp.ne.interspace.taekkyeon.model.InsertConversionRequest;
import jp.ne.interspace.taekkyeon.model.Pair;
import jp.ne.interspace.taekkyeon.model.PostbackKey;
import jp.ne.interspace.taekkyeon.model.PostbackStatus;
import jp.ne.interspace.taekkyeon.model.PublisherData;
import jp.ne.interspace.taekkyeon.model.RewardSettingKey;
import jp.ne.interspace.taekkyeon.model.RewardSettings;
import jp.ne.interspace.taekkyeon.module.Country;
import jp.ne.interspace.taekkyeon.module.Environment;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ConversionMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.PostbackUrlMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.PublisherAccountMapper;

import static com.google.common.base.Joiner.on;
import static com.google.common.base.Strings.isNullOrEmpty;
import static com.google.common.base.Strings.nullToEmpty;
import static java.util.Collections.emptySet;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.model.PostbackStatus.NEEDED;
import static jp.ne.interspace.taekkyeon.model.PostbackStatus.NOT_NEEDED;

/**
 * Service layer for handling conversion insertion.
 *
 * <AUTHOR> Shin
 */
@Singleton
public class ConversionInsertionService {

    private static final String DUPLICATION_CUT_PREFIX = "%s/%s/duplicationCut/%s";
    private static final long EXPIRE_DUPLICATION_CUT = 259_200; // 3days

    @Inject
    private RewardSettingService settingService;

    @Inject
    private RewardCalculatorService calculatorService;

    @Inject
    private PostbackUrlMapper postbackUrlMapper;

    @Inject
    private ConversionMapper conversionMapper;

    @Inject
    private PublisherAccountMapper publisherAccountMapper;

    @Inject
    private MerchantAccountService merchantAccountService;

    @Inject
    private JedisPool jedisPool;

    private LoadingCache<RewardSettingKey, Optional<RewardSettings>> rewardSettingCache =
            CacheBuilder
            .newBuilder().maximumSize(5000)
            .build(new CacheLoader<RewardSettingKey, Optional<RewardSettings>>() {

                @Override
                public Optional<RewardSettings> load(RewardSettingKey key)
                        throws Exception {
                    return Optional.ofNullable(findRewardSettingsFrom(key));
                }
            });

    private LoadingCache<PostbackKey, PostbackStatus> postbackStatusCache = CacheBuilder
            .newBuilder().maximumSize(5000)
            .build(new CacheLoader<PostbackKey, PostbackStatus>() {

                @Override
                public PostbackStatus load(PostbackKey key) throws Exception {
                    return postbackUrlMapper.isPostbackAvailableFor(key.getSiteId(),
                            key.getCreativeId()) ? NEEDED : NOT_NEEDED;
                }
            });

    private LoadingCache<Pair<Long, Long>, DuplicationCut> duplicationCutCache = CacheBuilder
            .newBuilder().maximumSize(5000)
            .build(new CacheLoader<Pair<Long, Long>, DuplicationCut>() {

                @Override
                public DuplicationCut load(Pair<Long, Long> key) throws Exception {
                    return conversionMapper.findDuplicationCutBy(key.getLeft(),
                            key.getRight());
                }
            });

    private LoadingCache<Long, PublisherData> publisherDataCache = CacheBuilder
            .newBuilder().maximumSize(5000).build(new CacheLoader<Long, PublisherData>() {

                @Override
                public PublisherData load(Long key) throws Exception {
                    return publisherAccountMapper.findPublisherDataBy(key);
                }
            });

    /**
     * Returns the {@link ConversionInsertion}s by the given {@link InsertConversionRequest}s.
     *
     * @param conversionRequests
     *          insertion conversion data
     * @return the {@link ConversionInsertion}s by the given {@link InsertConversionRequest}s
     * @throws Exception occurred error
     */
    public List<ConversionInsertion> convert(
            List<InsertConversionRequest> conversionRequests) throws Exception {
        List<ConversionInsertion> conversionInsertions = new LinkedList<>();
        for (InsertConversionRequest request : conversionRequests) {
            String merchantCountryCode = merchantAccountService
                    .findCountryCodeBy(request.getCampaignId());
            PublisherData publisherData = publisherDataCache.get(request.getSiteId());
            DuplicationCut duplicationCut = duplicationCutCache
                    .get(Pair.of(request.getCampaignId(), request.getSiteId()));
            ConversionInsertion conversionInsertion = createConversionInsertion(request,
                    merchantCountryCode, publisherData, duplicationCut);
            if (!isDuplicatedData(conversionInsertion)) {
                RewardSettings settings = rewardSettingCache
                        .get(new RewardSettingKey(request.getCampaignId(),
                                getHourTime(request.getConversionDate()),
                                request.getRank(),
                                request.getDeviceType(), request.getResultId(),
                                request.getProductId(), request.getCategoryId(),
                                request.getCustomerType()))
                        .orElse(null);
                conversionInsertions.add(calculate(conversionInsertion, settings));
            }
        }
        return conversionInsertions;
    }

    @VisibleForTesting
    ConversionInsertion createConversionInsertion(InsertConversionRequest request,
            String merchantCountryCode, PublisherData publisherData,
            DuplicationCut duplicationCut) {
        return new ConversionInsertion(request,
                publisherData, merchantCountryCode, duplicationCut);
    }

    @VisibleForTesting
    boolean isDuplicatedData(ConversionInsertion conversionInsertion) {
        String key = getDuplicationCutRedisKey(conversionInsertion);
        if (!isNullOrEmpty(key)) {
            try (Jedis jedis = jedisPool.getResource()) {
                return jedis.set(getDuplicationCutPrefix(key), "",
                        SetParams.setParams().ex(EXPIRE_DUPLICATION_CUT).nx()) == null;
            }
        }
        return false;
    }

    @VisibleForTesting
    String getDuplicationCutPrefix(String key) {
        return String.format(DUPLICATION_CUT_PREFIX,
                Country.getCurrentCountry().name().toLowerCase(),
                Environment.getCurrentEnvironment().name().toLowerCase(), key);
    }

    @VisibleForTesting
    String getDuplicationCutRedisKey(ConversionInsertion conversionInsertion) {
        DuplicationCut duplicationCut = conversionInsertion.getDuplicationCut();
        if (!duplicationCut.isOverlapFlag() && !duplicationCut.isVerifyCutFlag()) {
            return on("_").join(conversionInsertion.getCampaignId(),
                    conversionInsertion.getTransactionId());
        } else if (!duplicationCut.isOverlapFlag() && duplicationCut.isVerifyCutFlag()) {
            List<Object> join = new LinkedList<>();
            join.add(conversionInsertion.getCampaignId());
            join.add(conversionInsertion.getResultId());
            join.add(conversionInsertion.getTransactionId().substring(20));
            if (duplicationCut.getVerifyCutTarget() == 0) {
                join.add(conversionInsertion.getConversionTime().toLocalDate());
            }
            if (duplicationCut.getVerifyCutCondition() != 1) {
                join.add(nullToEmpty(conversionInsertion.getProductId()));
            }
            return on("_").join(join);
        } else if (duplicationCut.isOverlapFlag()) {
            if (!isNullOrEmpty(conversionInsertion.getUuid())) {
                return on("_").join(conversionInsertion.getCampaignId(),
                        conversionInsertion.getUuid(), conversionInsertion.getResultId());
            }
        }
        return EMPTY;
    }

    @VisibleForTesting
    ConversionInsertion calculate(ConversionInsertion conversion,
            RewardSettings settings) {
        YearMonth targetMonth = YearMonth.from(conversion.getConversionTime());
        ConversionRewards conversionRewards = calculatorService
                .calculateConversionRewardsBy(conversion, settings, emptySet(),
                        targetMonth);
        conversion.setAtCommission(conversionRewards.getAtCommission());
        conversion.setAgentCommission(conversionRewards.getAgentCommission());
        conversion.setPublisherAgentCommission(
                conversionRewards.getPublisherAgentCommission());
        conversion.setReward(conversionRewards.getSalesReward());
        conversion.setTransactionAmountReward(conversionRewards.getTotalPriceReward());
        conversion.setRewardType(
                calculatorService.getRewardType(settings, conversion.getRewardType()));
        conversion.setCommissionType(calculatorService.getCommissionType(settings,
                conversion.getCommissionType()));
        conversion.setAtCommissionInUsd(conversionRewards.getAtCommissionInUsd());
        conversion.setAgentCommissionInUsd(conversionRewards.getAgentCommissionInUsd());
        conversion.setPublisherAgentCommissionInUsd(
                conversionRewards.getPublisherAgentCommissionInUsd());
        conversion.setPublisherRewardInUsd(conversionRewards.getPublisherRewardInUsd());
        conversion
                .setTransactionAmountInUsd(conversionRewards.getTransactionAmountInUsd());
        conversion.setDiscountInUsd(conversionRewards.getDiscountInUsd());
        conversion.setUnitPriceInUsd(conversionRewards.getUnitPriceInUsd());
        conversion.setPostbackStatus(postbackStatusCache.getUnchecked(
                new PostbackKey(conversion.getSiteId(), conversion.getCreativeId())));
        return conversion;
    }

    @VisibleForTesting
    RewardSettings findRewardSettingsFrom(RewardSettingKey key) {
        return settingService.findRewardSettings(key.getCampaignId(), key.getTime(),
                key.getRank(), key.getDeviceType(), key.getResultId(), key.getProductId(),
                key.getCategoryId(), key.getCustomerType());
    }

    private LocalDateTime getHourTime(LocalDateTime time) {
        return LocalDateTime.of(time.getYear(), time.getMonth(), time.getDayOfMonth(),
                time.getHour(), 0);
    }
}
