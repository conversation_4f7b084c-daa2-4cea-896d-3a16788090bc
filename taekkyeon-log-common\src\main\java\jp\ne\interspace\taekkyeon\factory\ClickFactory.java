/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.factory;

import java.time.LocalDateTime;
import java.util.Map;

import javax.inject.Singleton;

import com.google.inject.Inject;

import jp.ne.interspace.taekkyeon.model.Click;
import jp.ne.interspace.taekkyeon.model.DeviceOs;
import jp.ne.interspace.taekkyeon.model.DeviceType;
import jp.ne.interspace.taekkyeon.model.LogItem;
import jp.ne.interspace.taekkyeon.service.AdvancedLogItemParserService;

/**
 * {@link LogItemFactory} implementation for {@link Click}s.
 *
 * <AUTHOR> Shin
 */
@Singleton
public class ClickFactory implements LogItemFactory {

    private static final String OPTIMIZER_UUID = "OPTIMIZER_UUID";
    private static final String LANGUAGE = "LANGUAGE";

    @Inject
    private AdvancedLogItemParserService parser;

    @Override
    public LogItem createFrom(String rawLogItem) {
        Map<String, String> parsedLogItem = parser.parse(rawLogItem);
        LocalDateTime logDate = parser.getLogDateFrom(parsedLogItem);
        long creativeId = parser.getCreativeIdFrom(parsedLogItem);
        long siteId = parser.getSiteIdFrom(parsedLogItem);
        long campaignId = parser.getCampaignIdFrom(parsedLogItem);
        DeviceType deviceType = parser.getDeviceTypeFrom(parsedLogItem);
        String ipAddress = parser.getIpAddressFrom(parsedLogItem);
        String referer = parser.getRefererFrom(parsedLogItem);
        String userAgent = parser.getUserAgentFrom(parsedLogItem);
        String trackingId = parser.getTrackingIdFrom(parsedLogItem);
        String productId = parser.getProductIdFrom(parsedLogItem);
        DeviceOs deviceOs = parser.getDeviceOsFrom(userAgent);
        String optimizerUuid = parsedLogItem.get(OPTIMIZER_UUID);
        String language = parsedLogItem.get(LANGUAGE);

        return new Click(logDate, creativeId, siteId, campaignId, deviceType, deviceOs,
                ipAddress, referer, userAgent, trackingId, productId, optimizerUuid,
                language);
    }
}
