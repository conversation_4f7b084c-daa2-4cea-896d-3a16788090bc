/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * Integration test for {@link ResultTargetSettingMapper}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class ResultTargetSettingMapperTest {

    @Inject
    private ResultTargetSettingMapper underTest;

    @Test
    public void testIsResultIdAvailableShouldReturnTrueWhenGivenResultIdOfGivenCampaignIdExists() {
        //given
        int resultId = 3;
        int campaignId = 1;

        //when
        boolean actual = underTest.isResultIdAvailable(resultId, campaignId);

        //then
        assertTrue(actual);
    }

    @Test
    public void testIsResultIdAvailableShouldReturnFalseWhenGivenResultIdOfGivenCampaignIdDoesNotExist() {
        //given
        int resultId = 1000;
        int campaignId = 1;

        //when
        boolean actual = underTest.isResultIdAvailable(resultId, campaignId);

        //then
        assertFalse(actual);
    }
}
