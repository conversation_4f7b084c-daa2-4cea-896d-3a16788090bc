/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.junit.Assert.assertEquals;

/**
 * Integration test for {@link CampaignMapper}.
 *
 * <AUTHOR> Shin
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class CampaignMapperTest {

    @Inject
    private CampaignMapper underTest;

    @Test
    public void testFindRefererCheckerByShouldReturnCorrectDataWhenCalled() {
        // given
        long campaignId = 1;

        // when
        String actual = underTest.findRefererCheckerBy(campaignId);

        // then
        assertEquals("google.co.jp,interspace.co.jp", actual);
    }
}
