/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.ConversionInsertion;
import jp.ne.interspace.taekkyeon.model.DeviceOs;
import jp.ne.interspace.taekkyeon.model.DeviceType;
import jp.ne.interspace.taekkyeon.model.DuplicationCut;
import jp.ne.interspace.taekkyeon.model.InsertConversionRequest;
import jp.ne.interspace.taekkyeon.model.PublisherData;

import static jp.ne.interspace.taekkyeon.model.PublisherAgencyCommissionPolicy.DONT_PAY;
import static org.junit.Assert.assertEquals;

/**
 * Integration test for {@link ConversionMapper}.
 *
 * <AUTHOR> Shin
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class ConversionMapperTest {

    private static final LocalDateTime LOG_DATE_TIME = LocalDateTime.of(2017, 10, 11, 15,
            10, 0);
    private static final long CREATIVE_ID = 1L;
    private static final long SITE_ID = 2L;
    private static final long CAMPAIGN_ID = 3L;
    private static final DeviceType DEVICE_TYPE = DeviceType.ANDROID;
    private static final DeviceOs DEVICE_OS = DeviceOs.ANDROID;
    private static final String IP_ADDRESS = "ipAddress";
    private static final String REFERER = "referer";
    private static final String USER_AGENT = "userAgent";
    private static final LocalDateTime CLICK_DATE_TIME = LocalDateTime.of(2017, 10, 10,
            15, 10, 0);
    private static final LocalDateTime CONVERSION_DATE_TIME = LocalDateTime.of(2017, 10,
            11, 15, 10, 5);
    private static final String SESSION_ID = "sessionId";
    private static final String IDENTIFIER = "identifier";
    private static final int RESULT_ID = 3;
    private static final BigDecimal PRICE = BigDecimal.valueOf(10);
    private static final BigDecimal DISCOUNT = BigDecimal.valueOf(9);
    private static final String PRODUCT_ID = "productId";
    private static final String CATEGORY_ID = "categoryId";
    private static final String POINTBACK_ID = "pointbackId";
    private static final String CLICK_REFERER = "clickReferer";
    private static final String CLICK_URL = "clickUrl";
    private static final String CLICK_USER_AGENT = "clickUserAgent";
    private static final String CUSTOMER_TYPE = "customerType";
    private static final String CURRENCY = "USD";
    private static final String LANGUAGE = "language";
    private static final String CLICK_IP_ADDRESS = "clickIpAddress";

    @Inject
    private ConversionMapper underTest;

    @Test
    public void testInsertShouldReturnOneWhenOverlapFlagIsFalseAndVerifyCutFlagIsFalse() {
        // given
        InsertConversionRequest conversionRequest = createInsertConversionRequest();
        PublisherData publisherData = new PublisherData("ID", DONT_PAY);
        DuplicationCut duplicationCut = new DuplicationCut(false, false, 0, 0);
        ConversionInsertion conversionInsertion = new ConversionInsertion(
                conversionRequest, publisherData, "ID", duplicationCut);

        // when
        int actual = underTest.insert(conversionInsertion);

        // then
        assertEquals(1, actual);
    }

    @Test
    public void testInsertShouldReturnOneWhenOverlapFlagIsFalseAndVerifyCutFlagIsTrue() {
        // given
        InsertConversionRequest conversionRequest = createInsertConversionRequest();
        PublisherData publisherData = new PublisherData("ID", DONT_PAY);
        DuplicationCut duplicationCut = new DuplicationCut(false, true, 0, 0);
        ConversionInsertion conversionInsertion = new ConversionInsertion(
                conversionRequest, publisherData, "ID", duplicationCut);

        // when
        int actual = underTest.insert(conversionInsertion);

        // then
        assertEquals(1, actual);
    }

    @Test
    public void testInsertShouldReturnOneWhenOverlapFlagIsFalseAndVerifyCutFlagIsTrueAndVerifyCutTargetIsOne() {
        // given
        InsertConversionRequest conversionRequest = createInsertConversionRequest();
        PublisherData publisherData = new PublisherData("ID", DONT_PAY);
        DuplicationCut duplicationCut = new DuplicationCut(false, true, 1, 0);
        ConversionInsertion conversionInsertion = new ConversionInsertion(
                conversionRequest, publisherData, "ID", duplicationCut);

        // when
        int actual = underTest.insert(conversionInsertion);

        // then
        assertEquals(1, actual);
    }

    @Test
    public void testInsertShouldReturnOneWhenOverlapFlagIsFalseAndVerifyCutFlagIsTrueAndVerifyCutTargetIsOneAndVerifyCutConditionIsOne() {
        // given
        InsertConversionRequest conversionRequest = createInsertConversionRequest();
        PublisherData publisherData = new PublisherData("ID", DONT_PAY);
        DuplicationCut duplicationCut = new DuplicationCut(false, true, 1, 1);
        ConversionInsertion conversionInsertion = new ConversionInsertion(
                conversionRequest, publisherData, "ID", duplicationCut);

        // when
        int actual = underTest.insert(conversionInsertion);

        // then
        assertEquals(1, actual);
    }

    @Test
    public void testInsertShouldReturnOneWhenOverlapFlagIsTrue() {
        // given
        InsertConversionRequest conversionRequest = createInsertConversionRequest();
        PublisherData publisherData = new PublisherData("ID", DONT_PAY);
        DuplicationCut duplicationCut = new DuplicationCut(true, false, 0, 0);
        ConversionInsertion conversionInsertion = new ConversionInsertion(
                conversionRequest, publisherData, "ID", duplicationCut);

        // when
        int actual = underTest.insert(conversionInsertion);

        // then
        assertEquals(1, actual);
    }

    @Test
    public void testFindDuplicationCutByShouldReturnCorrectDataWhenCalled() {
        // when
        DuplicationCut actual = underTest.findDuplicationCutBy(1, 1);

        // then
        assertEquals(new DuplicationCut(true, true, 1, 1), actual);
    }

    private InsertConversionRequest createInsertConversionRequest() {
        return new InsertConversionRequest(LOG_DATE_TIME, CREATIVE_ID, SITE_ID,
                CAMPAIGN_ID, DEVICE_TYPE, DEVICE_OS, IP_ADDRESS, REFERER, USER_AGENT,
                CLICK_DATE_TIME, CONVERSION_DATE_TIME,
                java.util.UUID.randomUUID().toString(), "internalTransactionId",
                SESSION_ID, 5, IDENTIFIER, RESULT_ID, PRICE, CURRENCY, PRICE,
                POINTBACK_ID, PRODUCT_ID, java.util.UUID.randomUUID().toString(),
                CATEGORY_ID, DISCOUNT, CLICK_REFERER, CLICK_URL, CLICK_USER_AGENT,
                CUSTOMER_TYPE, LANGUAGE, CLICK_IP_ADDRESS);
    }
}
