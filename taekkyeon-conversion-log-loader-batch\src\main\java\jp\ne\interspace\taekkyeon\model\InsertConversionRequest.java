/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * DTO for holding the data of a conversion insertion request.
 *
 * <AUTHOR>
 */
@Getter @Setter @ToString(callSuper = true)
public class InsertConversionRequest extends LogItem {

    private final LocalDateTime clickDate;
    private final LocalDateTime conversionDate;
    private final String transactionId;
    private final String internalTransactionId;
    private final String sessionId;
    private final int rank;
    private final String identifier;
    private final int resultId;
    private final BigDecimal defaultPrice;
    private final String originalCurrency;
    private final BigDecimal originalCurrencyTotalPrice;
    private final String pointbackId;
    private final String productId;
    private final String uuid;
    private final DeviceOs deviceOs;
    private final String categoryId;
    private final BigDecimal discount;
    private final String clickReferer;
    private final String clickUrl;
    private final String clickUserAgent;
    private final String customerType;
    private final String language;
    private final String clickIpAddress;

    /**
     * Constructor to be used by the relevant MyBatis mapper. Cannot be auto-generated
     * because of the {@code super()} call.
     */
    public InsertConversionRequest(LocalDateTime logDate, long creativeId, long siteId,
            long campaignId, DeviceType deviceType, DeviceOs deviceOs, String ipAddress,
            String referer, String userAgent, LocalDateTime clickDate,
            LocalDateTime conversionDate, String transactionId,
            String internalTransactionId, String sessionId, int rank, String identifier,
            int resultId, BigDecimal defaultPrice, String originalCurrency,
            BigDecimal originalCurrencyTotalPrice, String pointbackId, String productId,
            String uuid, String categoryId, BigDecimal discount, String clickReferer,
            String clickUrl, String clickUserAgent, String customerType,
            String language, String clickIpAdress) {
        super(logDate, creativeId, siteId, campaignId, deviceType, deviceOs, ipAddress,
                referer, userAgent);
        this.clickDate = clickDate;
        this.conversionDate = conversionDate;
        this.transactionId = transactionId;
        this.internalTransactionId = internalTransactionId;
        this.sessionId = sessionId;
        this.rank = rank;
        this.identifier = identifier;
        this.resultId = resultId;
        this.defaultPrice = defaultPrice;
        this.originalCurrency = originalCurrency;
        this.originalCurrencyTotalPrice = originalCurrencyTotalPrice;
        this.pointbackId = pointbackId;
        this.productId = productId;
        this.uuid = uuid;
        this.deviceOs = deviceOs;
        this.categoryId = categoryId;
        this.discount = discount;
        this.clickReferer = clickReferer;
        this.clickUrl = clickUrl;
        this.clickUserAgent = clickUserAgent;
        this.customerType = customerType;
        this.language = language;
        this.clickIpAddress = clickIpAdress;
    }
}
