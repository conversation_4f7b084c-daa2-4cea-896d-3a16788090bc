/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.factory;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;

import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.inject.Inject;

import jp.ne.interspace.taekkyeon.model.CampaignSettingDuplicationCutDetails;
import jp.ne.interspace.taekkyeon.model.Conversion;
import jp.ne.interspace.taekkyeon.model.CurrencyRate;
import jp.ne.interspace.taekkyeon.model.InsertConversionRequest;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.AffiliationRankHistoryMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CurrencyMapper;
import jp.ne.interspace.taekkyeon.service.CampaignSettingService;

import static java.math.BigDecimal.ROUND_HALF_UP;
import static java.math.BigDecimal.ZERO;
import static java.util.Objects.isNull;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DOLLAR;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.PIPE;
import static jp.ne.interspace.taekkyeon.model.CurrencyRate.DEFAULT_CURRENCY_RATE;
import static jp.ne.interspace.taekkyeon.model.DuplicationCutTarget.DAILY;


/**
 * Factory class for returning a {@link InsertConversionRequest}s.
 *
 * <AUTHOR> Shin
 */
@Singleton
public class InsertConversionRequestFactory {

    private static final int UNDEFINED_RANK = 0;
    private static final int INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID = 3;
    private static final int INDIVIDUAL_PURCHASE_PRODUCT_CATEGORY_RESULT_ID = 30;

    @Inject
    private CurrencyMapper currencyMapper;

    @Inject
    private CampaignSettingService campaignSettingService;

    @Inject
    private AffiliationRankHistoryMapper affiliationMapper;

    private LoadingCache<String, Integer> rankCache = CacheBuilder.newBuilder()
            .maximumSize(5000)
            .build(new CacheLoader<String, Integer>() {
                @Override
                public Integer load(String key) throws Exception {
                    return findRankBy(key);
                }
            });

    private LoadingCache<String, CurrencyRate> campaignCurrencyCache = CacheBuilder.newBuilder()
            .maximumSize(5000)
            .build(new CacheLoader<String, CurrencyRate>() {
                @Override
                public CurrencyRate load(String key) throws Exception {
                    return findCampaignCurrencyRateBy(key);
                }
            });

    private LoadingCache<String, CurrencyRate> currencyCache = CacheBuilder.newBuilder()
            .maximumSize(5000)
            .build(new CacheLoader<String, CurrencyRate>() {
                @Override
                public CurrencyRate load(String key) throws Exception {
                    return findCurrencyRateBy(key);
                }
            });

    /**
     * Returns the {@link InsertConversionRequest}s for inserting to the database.
     *
     * @param conversion
     *          the details of the conversion
     * @return the {@link InsertConversionRequest}s for inserting to the database
     * @throws Exception when a problem occurs
     */
    public List<InsertConversionRequest> createInsertRequestsOf(Conversion conversion)
            throws Exception {
        long campaignId = conversion.getCampaignId();
        LocalDateTime conversionDate = conversion.getConversionDate();
        YearMonth conversionMonth = YearMonth.of(conversionDate.getYear(),
                conversionDate.getMonth());
        int rank = rankCache.get(createRankKeyBy(campaignId, conversion.getSiteId(),
                conversionDate));
        CurrencyRate currencyRate = getCurrencyRateFrom(conversion.getCurrency(),
                conversion.getCampaignId(), conversionMonth);
        BigDecimal originalCurrencyUnitPrice =
                getOriginalCurrencyUnitPriceFrom(conversion);
        BigDecimal discount = getDiscountFrom(conversion);
        BigDecimal defaultCurrencyUnitPrice = originalCurrencyUnitPrice.multiply(
                currencyRate.getRate());

        String internalTransactionId = createInternalTransactionIdBy(campaignId,
                conversionDate.toLocalDate(), conversion.getIdentifier(),
                conversion.getResultId(), conversion.getCustomerType());

        return createConversionRequests(conversion, internalTransactionId,
                currencyRate.getCurrency(), rank, discount, originalCurrencyUnitPrice,
                defaultCurrencyUnitPrice);
    }

    @VisibleForTesting
    CurrencyRate getCurrencyRateFrom(String currency, long campaignId,
            YearMonth conversionMonth) throws ExecutionException {
        CurrencyRate currencyRate = DEFAULT_CURRENCY_RATE;
        if (!EMPTY.equals(currency)) {
            currencyRate = currencyCache.get(createCustomKey(currency, campaignId,
                    conversionMonth));
        }
        if (EMPTY.equals(currency) || currencyRate.getCurrency() == null) {
            currencyRate = campaignCurrencyCache.get(createCurrencyKeyBy(campaignId,
                    conversionMonth));
        }
        return currencyRate;
    }

    @VisibleForTesting
    String createInternalTransactionIdBy(long campaignId, LocalDate conversionDate,
            String identifier, int resultId, String customerType) {
        CampaignSettingDuplicationCutDetails duplicationCutDetails =
                campaignSettingService.findDuplicationCutDetailsBy(campaignId);
        if (duplicationCutDetails.isEnabled()) {
            String permanentTargetInternalTransactionId =
                    createPermanentTargetInternalTransactionId(campaignId, identifier,
                            resultId, customerType);
            return duplicationCutDetails.getTarget() == DAILY
                    ? Joiner.on(DOLLAR).join(conversionDate,
                            permanentTargetInternalTransactionId)
                    : permanentTargetInternalTransactionId;
        } else {
            return UUID.randomUUID().toString();
        }
    }

    @VisibleForTesting
    String createPermanentTargetInternalTransactionId(long campaignId, String identifier,
            int resultId, String customerType) {
        return Joiner.on(DOLLAR).skipNulls().join(campaignId, identifier, resultId,
                Strings.emptyToNull(customerType));
    }

    @VisibleForTesting
    BigDecimal getDiscountFrom(Conversion conversion) {
        if (isDiscountable(conversion)) {
            return getOriginalUnitPriceFrom(conversion)
                    .multiply(conversion.getDiscount())
                    .divide(conversion.getOriginalTotalPrice(), 2, ROUND_HALF_UP);
        }
        return conversion.getDiscount();
    }

    @VisibleForTesting
    boolean isDiscountable(Conversion conversion) {
        return conversion.getOriginalTotalPrice().compareTo(ZERO) > 0
                && getOriginalUnitPriceFrom(conversion).compareTo(ZERO) > 0
                && conversion.getQuantity() > 0
                && conversion.getDiscount().compareTo(ZERO) > 0;
    }

    @VisibleForTesting
    BigDecimal getOriginalUnitPriceFrom(Conversion conversion) {
        if (isProductOrCategoryReward(conversion.getResultId())) {
            return conversion.getUnitPrice().compareTo(ZERO) > 0
                    ? conversion.getUnitPrice() : ZERO;
        }
        return conversion.getPrice();
    }

    private boolean isProductOrCategoryReward(int resultId) {
        return resultId == INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID
                || resultId == INDIVIDUAL_PURCHASE_PRODUCT_CATEGORY_RESULT_ID;
    }

    private List<InsertConversionRequest> createConversionRequests(Conversion conversion,
            String internalTransactionId, String currency, int rank,
            BigDecimal discount, BigDecimal originalCurrencyUnitPrice,
            BigDecimal defaultCurrencyUnitPrice) {
        List<InsertConversionRequest> conversionRequests = new ArrayList<>();
        for (String transactionId : conversion.getTransactionIds()) {
            InsertConversionRequest conversionRequest = createInsertConversionRequestBy(
                    conversion, transactionId, internalTransactionId, currency, rank,
                    discount, originalCurrencyUnitPrice, defaultCurrencyUnitPrice);
            conversionRequests.add(conversionRequest);
        }
        return conversionRequests;
    }

    private BigDecimal getOriginalCurrencyUnitPriceFrom(Conversion conversion) {
        if (isDiscountable(conversion)) {
            return getDiscountedPriceFrom(conversion);
        }
        return getOriginalUnitPriceFrom(conversion);
    }

    @VisibleForTesting
    BigDecimal getDiscountedPriceFrom(Conversion conversion) {
        BigDecimal originalUnitPrice = getOriginalUnitPriceFrom(conversion);
        BigDecimal discount = getDiscountFrom(conversion);
        BigDecimal discountedPrice = originalUnitPrice.subtract(discount);
        return discountedPrice.compareTo(ZERO) > 0 ? discountedPrice : ZERO;
    }

    private InsertConversionRequest createInsertConversionRequestBy(
            Conversion conversion, String transactionId, String internalTransactionId,
            String currency, int rank, BigDecimal discount,
            BigDecimal originalCurrencyUnitPrice, BigDecimal defaultCurrencyUnitPrice) {
        return new InsertConversionRequest(
                    conversion.getLogDate(),
                    conversion.getCreativeId(),
                    conversion.getSiteId(),
                    conversion.getCampaignId(),
                    conversion.getDeviceType(),
                    conversion.getDeviceOs(),
                    conversion.getIpAddress(),
                    conversion.getReferer(),
                    conversion.getUserAgent(),
                    conversion.getClickDate(),
                    conversion.getConversionDate(),
                    transactionId,
                    internalTransactionId,
                    conversion.getSessionId(),
                    rank,
                    conversion.getIdentifier(),
                    conversion.getResultId(),
                    defaultCurrencyUnitPrice.setScale(2, ROUND_HALF_UP),
                    currency,
                    originalCurrencyUnitPrice.setScale(2, ROUND_HALF_UP),
                    conversion.getPointbackId(),
                    conversion.getProductId(),
                    conversion.getUuid(),
                    conversion.getCategoryId(),
                    discount,
                    conversion.getClickReferer(),
                    conversion.getClickUrl(),
                    conversion.getClickUserAgent(),
                    conversion.getCustomerType(),
                    conversion.getLanguage(),
                    conversion.getClickIpAddress());
    }

    private CurrencyRate findCampaignCurrencyRateBy(String key) {
        List<String> splits = Splitter.on(PIPE).splitToList(key);
        long campaignId = Long.parseLong(splits.get(0));
        YearMonth conversionMonth = YearMonth.parse(splits.get(1));
        return currencyMapper.findCampaignCurrencyRate(campaignId, conversionMonth);
    }

    private CurrencyRate findCurrencyRateBy(String key) {
        List<String> splits = Splitter.on(PIPE).splitToList(key);
        String currency = splits.get(0);
        long campaignId = Long.parseLong(splits.get(1));
        YearMonth conversionMonth = YearMonth.parse(splits.get(2));
        return currencyMapper.findCurrencyRate(currency, campaignId, conversionMonth);
    }

    private String createCurrencyKeyBy(long campaignId, YearMonth conversionMonth) {
        return createCustomKey(campaignId, conversionMonth);
    }

    private int findRankBy(String key) {
        List<String> split = Splitter.on(PIPE).splitToList(key);
        long campaignId = Long.parseLong(split.get(0));
        long siteId = Long.parseLong(split.get(1));
        LocalDateTime conversionDate = LocalDateTime.parse(split.get(2));
        Integer rank = affiliationMapper.findRankBy(campaignId, siteId, conversionDate);
        if (!isNull(rank)) {
            return rank;
        }
        return UNDEFINED_RANK;
    }

    private String createRankKeyBy(long campaignId, long siteId,
            LocalDateTime conversionDate) {
        return createCustomKey(campaignId, siteId, conversionDate);
    }

    private String createCustomKey(Object... objects) {
        return Joiner.on(PIPE).join(objects);
    }
}
