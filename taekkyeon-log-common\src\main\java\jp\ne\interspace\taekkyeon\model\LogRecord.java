/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import org.easybatch.core.record.GenericRecord;
import org.easybatch.core.record.Header;
import org.easybatch.core.record.Record;

/**
 * {@link Record} for holding log items.
 *
 * <AUTHOR>
 */
public class LogRecord extends GenericRecord<LogRecordPayload> {

    public LogRecord(Header header, LogRecordPayload payload) {
        super(header, payload);
    }
}
