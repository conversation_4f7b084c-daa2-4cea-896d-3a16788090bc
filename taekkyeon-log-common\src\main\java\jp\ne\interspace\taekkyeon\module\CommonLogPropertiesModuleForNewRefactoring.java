/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import javax.inject.Singleton;

import com.google.common.collect.ImmutableMap;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;

import static java.lang.String.format;
import static java.lang.System.getProperty;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.module.Country.INDONESIA;
import static jp.ne.interspace.taekkyeon.module.Country.THAILAND;
import static jp.ne.interspace.taekkyeon.module.Country.VIETNAM;
import static jp.ne.interspace.taekkyeon.module.Country.getCurrentCountry;
import static jp.ne.interspace.taekkyeon.module.Environment.DEV;
import static jp.ne.interspace.taekkyeon.module.Environment.PRODUCTION;
import static jp.ne.interspace.taekkyeon.module.Environment.STAGING;
import static jp.ne.interspace.taekkyeon.module.Environment.getCurrentEnvironment;
import static jp.ne.interspace.taekkyeon.module.LogType.CLICK;
import static jp.ne.interspace.taekkyeon.module.LogType.CONVERSION;
import static jp.ne.interspace.taekkyeon.module.LogType.IMPRESSION;
import static jp.ne.interspace.taekkyeon.module.LogType.getCurrentLogType;

/**
 * Guice module holding common configurations for new refactoring log loaders and providers.
 *
 * <AUTHOR> Tran
 */
public class CommonLogPropertiesModuleForNewRefactoring extends AbstractModule {
    private static final String QUEUE_NAME_FORMAT = "%s-%s-%s-log-name-provider";
    private static final String VM_ARGUMENT_FOR_TARGET_SQS_NAME = "targetSqsName";

    private static final ImmutableMap<BaseSqsNameKey, String> BASE_SQS_NAMES = new ImmutableMap.Builder<BaseSqsNameKey, String>()
            .put(BaseSqsNameKey.of(THAILAND, DEV, IMPRESSION), "thailand-imp-dev")
            .put(BaseSqsNameKey.of(THAILAND, DEV, CLICK), "thailand-cl-dev")
            .put(BaseSqsNameKey.of(THAILAND, DEV, CONVERSION), "thailand-cv-vip-dev")
            .put(BaseSqsNameKey.of(THAILAND, STAGING, IMPRESSION), "thailand-imp-staging")
            .put(BaseSqsNameKey.of(THAILAND, STAGING, CLICK), "thailand-cl-staging")
            .put(BaseSqsNameKey.of(THAILAND, STAGING, CONVERSION), "thailand-cv-vip-staging")
            .put(BaseSqsNameKey.of(THAILAND, PRODUCTION, IMPRESSION), "thailand-imp")
            .put(BaseSqsNameKey.of(THAILAND, PRODUCTION, CLICK), "thailand-cl")
            .put(BaseSqsNameKey.of(THAILAND, PRODUCTION, CONVERSION), "thailand-cv-vip")
            .put(BaseSqsNameKey.of(VIETNAM, DEV, IMPRESSION), "vietnam-imp-dev")
            .put(BaseSqsNameKey.of(VIETNAM, DEV, CLICK), "vietnam-cl-dev")
            .put(BaseSqsNameKey.of(VIETNAM, DEV, CONVERSION), "vietnam-cv-vip-dev")
            .put(BaseSqsNameKey.of(VIETNAM, STAGING, IMPRESSION), "vietnam-imp-staging")
            .put(BaseSqsNameKey.of(VIETNAM, STAGING, CLICK), "vietnam-cl-staging")
            .put(BaseSqsNameKey.of(VIETNAM, STAGING, CONVERSION), "vietnam-cv-vip-staging")
            .put(BaseSqsNameKey.of(VIETNAM, PRODUCTION, IMPRESSION), "vietnam-imp")
            .put(BaseSqsNameKey.of(VIETNAM, PRODUCTION, CLICK), "vietnam-cl")
            .put(BaseSqsNameKey.of(VIETNAM, PRODUCTION, CONVERSION), "vietnam-cv-vip")
            .put(BaseSqsNameKey.of(INDONESIA, DEV, IMPRESSION), "indonesia-imp-dev")
            .put(BaseSqsNameKey.of(INDONESIA, DEV, CLICK), "indonesia-cl-dev")
            .put(BaseSqsNameKey.of(INDONESIA, DEV, CONVERSION), "indonesia-cv-vip-dev")
            .put(BaseSqsNameKey.of(INDONESIA, STAGING, IMPRESSION), "indonesia-imp-staging")
            .put(BaseSqsNameKey.of(INDONESIA, STAGING, CLICK), "indonesia-cl-staging")
            .put(BaseSqsNameKey.of(INDONESIA, STAGING, CONVERSION), "indonesia-cv-vip-staging")
            .put(BaseSqsNameKey.of(INDONESIA, PRODUCTION, IMPRESSION), "indonesia-imp")
            .put(BaseSqsNameKey.of(INDONESIA, PRODUCTION, CLICK), "indonesia-cl")
            .put(BaseSqsNameKey.of(INDONESIA, PRODUCTION, CONVERSION), "indonesia-cv-vip")
            .build();

    private static final ImmutableMap<BaseSqsNameKey, String> BASE_SQS_NAMES_BY_DEFAULT = new ImmutableMap.Builder<BaseSqsNameKey, String>()
            .put(BaseSqsNameKey.of(THAILAND, DEV, IMPRESSION), "thailand-imp-dev")
            .put(BaseSqsNameKey.of(THAILAND, DEV, CLICK), "thailand-cl-dev")
            .put(BaseSqsNameKey.of(THAILAND, DEV, CONVERSION), "thailand-cv-dev")
            .put(BaseSqsNameKey.of(THAILAND, STAGING, IMPRESSION), "thailand-imp-staging")
            .put(BaseSqsNameKey.of(THAILAND, STAGING, CLICK), "thailand-cl-staging")
            .put(BaseSqsNameKey.of(THAILAND, STAGING, CONVERSION), "thailand-cv-staging")
            .put(BaseSqsNameKey.of(THAILAND, PRODUCTION, IMPRESSION), "thailand-imp")
            .put(BaseSqsNameKey.of(THAILAND, PRODUCTION, CLICK), "thailand-cl")
            .put(BaseSqsNameKey.of(THAILAND, PRODUCTION, CONVERSION), "thailand-cv")
            .put(BaseSqsNameKey.of(VIETNAM, DEV, IMPRESSION), "vietnam-imp-dev")
            .put(BaseSqsNameKey.of(VIETNAM, DEV, CLICK), "vietnam-cl-dev")
            .put(BaseSqsNameKey.of(VIETNAM, DEV, CONVERSION), "vietnam-cv-dev")
            .put(BaseSqsNameKey.of(VIETNAM, STAGING, IMPRESSION), "vietnam-imp-staging")
            .put(BaseSqsNameKey.of(VIETNAM, STAGING, CLICK), "vietnam-cl-staging")
            .put(BaseSqsNameKey.of(VIETNAM, STAGING, CONVERSION), "vietnam-cv-staging")
            .put(BaseSqsNameKey.of(VIETNAM, PRODUCTION, IMPRESSION), "vietnam-imp")
            .put(BaseSqsNameKey.of(VIETNAM, PRODUCTION, CLICK), "vietnam-cl")
            .put(BaseSqsNameKey.of(VIETNAM, PRODUCTION, CONVERSION), "vietnam-cv")
            .put(BaseSqsNameKey.of(INDONESIA, DEV, IMPRESSION), "indonesia-imp-dev")
            .put(BaseSqsNameKey.of(INDONESIA, DEV, CLICK), "indonesia-cl-dev")
            .put(BaseSqsNameKey.of(INDONESIA, DEV, CONVERSION), "indonesia-cv-dev")
            .put(BaseSqsNameKey.of(INDONESIA, STAGING, IMPRESSION), "indonesia-imp-staging")
            .put(BaseSqsNameKey.of(INDONESIA, STAGING, CLICK), "indonesia-cl-staging")
            .put(BaseSqsNameKey.of(INDONESIA, STAGING, CONVERSION), "indonesia-cv-staging")
            .put(BaseSqsNameKey.of(INDONESIA, PRODUCTION, IMPRESSION), "indonesia-imp")
            .put(BaseSqsNameKey.of(INDONESIA, PRODUCTION, CLICK), "indonesia-cl")
            .put(BaseSqsNameKey.of(INDONESIA, PRODUCTION, CONVERSION), "indonesia-cv")
            .build();

    @Override
    protected void configure() {
    }

    @Provides @Singleton @LogQueueNameResolver
    private String provideBaseQueueName() {
        return BASE_SQS_NAMES.get(BaseSqsNameKey.of(getCurrentCountry(),
                getCurrentEnvironment(), getCurrentLogType()));
    }

    @Provides @Singleton @LogQueueNameResolverByDefault
    private String provideBaseQueueNameByDefault() {
        if (getCurrentLogType().equals(CONVERSION)) {
            String targetSqsName = getProperty(VM_ARGUMENT_FOR_TARGET_SQS_NAME, EMPTY);
            if (!targetSqsName.isEmpty()) {
                return targetSqsName;
            }
        }
        return BASE_SQS_NAMES_BY_DEFAULT.get(BaseSqsNameKey.of(
                getCurrentCountry(), getCurrentEnvironment(), getCurrentLogType()));
    }

    @Provides @Singleton @LogNameProviderQueueResolver
    private String provideQueueName() {
        return format(QUEUE_NAME_FORMAT,
                getCurrentCountry().name().toLowerCase(),
                getCurrentEnvironment().name().toLowerCase(),
                getCurrentLogType().getCode());
    }
}
