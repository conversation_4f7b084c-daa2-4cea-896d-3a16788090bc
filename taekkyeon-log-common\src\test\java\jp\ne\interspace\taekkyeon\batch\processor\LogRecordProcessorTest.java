/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.processor;

import org.easybatch.core.record.Header;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import jp.ne.interspace.taekkyeon.factory.LogItemFactory;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.LogItem;
import jp.ne.interspace.taekkyeon.model.LogRecord;
import jp.ne.interspace.taekkyeon.model.SqsRecord;
import jp.ne.interspace.taekkyeon.model.SqsRecordPayload;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link LogRecordProcessor}.
 *
 * <AUTHOR> Varga
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class LogRecordProcessorTest {

    private static final String RECEIPT_HANDLE = "receiptHandle";
    private static final String RAW_LOG_ITEM = "rawLogItem";

    @InjectMocks
    private LogRecordProcessor underTest;

    @Mock
    private LogItemFactory logItemFactory;

    @Test
    public void testProcessRecordShouldReturnCorrectRecordWhenRawLogItemIsParsedSuccessfully()
            throws Exception {
        // given
        LogItem expectedLogItem = mock(LogItem.class);
        when(logItemFactory.createFrom(RAW_LOG_ITEM)).thenReturn(expectedLogItem);

        Header header = mock(Header.class);
        SqsRecord record = new SqsRecord(header,
                new SqsRecordPayload(RECEIPT_HANDLE, RAW_LOG_ITEM));

        // when
        LogRecord actual = underTest.processRecord(record);

        // then
        assertNotNull(actual);
        assertSame(header, actual.getHeader());

        assertNotNull(actual.getPayload());
        assertEquals(RECEIPT_HANDLE, actual.getPayload().getReceiptHandle());
        assertSame(expectedLogItem, actual.getPayload().getLogItem());
    }

    @Test
    public void testProcessRecordShouldReturnNullLogItemWhenRawLogItemParsingFails()
            throws Exception {
        // given
        when(logItemFactory.createFrom(RAW_LOG_ITEM)).thenThrow(new RuntimeException());

        Header header = mock(Header.class);
        SqsRecord record = new SqsRecord(header,
                new SqsRecordPayload(RECEIPT_HANDLE, RAW_LOG_ITEM));

        // when
        LogRecord actual = underTest.processRecord(record);

        // then
        assertNotNull(actual);
        assertEquals(RECEIPT_HANDLE, actual.getPayload().getReceiptHandle());
        assertNull(actual.getPayload().getLogItem());
    }
}
