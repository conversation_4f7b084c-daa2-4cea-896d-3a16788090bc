/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.model.ConversionInsertion;
import jp.ne.interspace.taekkyeon.model.DuplicationCut;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * MyBatis mapper for handling conversion log database operations.
 *
 * <AUTHOR> Shin
 */
public interface ConversionMapper {

    String START_TAG_SCRIPT = "<script>";
    String END_TAG_SCRIPT = "</script>";

    /**
        INSERT INTO
            sales_log (
                seq_no,
                banner_id,
                merchant_campaign_no,
                click_date,
                sales_date,
                log_date,
                transaction_id,
                internal_transaction_id,
                session_id,
                partner_site_no,
                rank,
                verify,
                result_id,
                goods_id,
                sales_log_status,
                sales_count,
                price,
                total_price,
                original_currency_total_price,
                original_currency,
                reward_type,
                sales_reward,
                total_price_reward,
                commission_type,
                at_commission,
                agent_commission,
                p_agent_commission,
                ip,
                referer,
                user_agent,
                default_sales_count,
                default_price,
                device_type,
                pointback_id,
                pb_id_duplicative_flag,
                uuid,
                device_os,
                category_id,
                discount,
                click_referer,
                click_url,
                click_user_agent,
                customer_type,
                postback_status,
                reward_edit_date,
                created_by,
                created_on,
                publisher_reward_in_usd,
                transaction_amount_in_usd,
                at_commission_in_usd,
                merchant_agent_commission_in_usd,
                publisher_agent_commission_in_usd,
                discount_amount_in_usd,
                unit_price_in_usd,
                language,
                click_ip
            )
            SELECT SALES_LOG_SEQ.NEXTVAL, slts.*
              FROM (
                SELECT
                    #{creativeId} AS creative_id,
                    #{campaignId} AS merchant_campaign_no,
                    #{clickTime, jdbcType=DATE} AS click_date,
                    #{conversionTime, jdbcType=DATE} AS sales_date,
                    #{logTime, jdbcType=DATE} AS log_date,
                    #{transactionId} AS transaction_id,
                    #{internalTransactionId} AS internal_transaction_id,
                    #{sessionId, jdbcType=VARCHAR} AS session_id,
                    #{siteId} AS site_id,
                    #{rank} AS rank,
                    #{verify} AS verify,
                    #{resultId} AS result_id,
                    #{productId, jdbcType=VARCHAR} AS goods_id,
                    #{conversionStatus.value} AS conversion_status,
                    #{count} AS sales_count,
                    #{price} AS price,
                    #{totalPrice} AS total_price,
                    #{originalCurrencyTotalPrice, jdbcType=NUMERIC} AS original_currency_total_price,
                    #{originalCurrency, jdbcType=VARCHAR} AS original_currency,
                    #{rewardType.value} AS reward_type,
                    #{reward} AS reward,
                    #{transactionAmountReward} AS transaction_amount_reward,
                    #{commissionType.value} AS commission_type,
                    #{atCommission} AS at_commission,
                    #{agentCommission} AS agent_commission,
                    #{publisherAgentCommission} AS publisher_agent_commission,
                    #{ipAddress, jdbcType=VARCHAR} AS ip_address,
                    #{referer, jdbcType=VARCHAR} AS referer,
                    #{userAgent, jdbcType=VARCHAR} AS user_agent,
                    #{defaultConversionCount} AS default_conversion_count,
                    #{defaultPrice} AS default_price,
                    #{deviceType.value} AS device_type,
                    #{pointbackId, jdbcType=VARCHAR} AS pointback_id,
                    1 AS pb_id_duplicative_flag,
                    #{uuid, jdbcType=VARCHAR} AS uuid,
                    #{deviceOs.value} AS device_os,
                    #{categoryId, jdbcType=VARCHAR} AS category_id,
                    #{discount} AS discount,
                    #{clickReferer, jdbcType=VARCHAR} AS click_referer,
                    #{clickUrl, jdbcType=VARCHAR} AS click_url,
                    #{clickUserAgent, jdbcType=VARCHAR} AS click_user_agent,
                    #{customerType, jdbcType=VARCHAR} AS customer_type,
                    #{postbackStatus.value} AS postback_status,
                    SYSDATE AS reward_edit_date,
                    'ConversionRegistrationBatch' AS created_by,
                    SYSDATE AS created_on,
                    #{publisherRewardInUsd} AS publisher_reward_in_usd,
                    #{transactionAmountInUsd} AS transaction_amount_in_usd,
                    #{atCommissionInUsd} AS at_commission_in_usd,
                    #{agentCommissionInUsd} AS agent_commission_in_usd,
                    #{publisherAgentCommissionInUsd} AS publisher_agent_commission_in_usd,
                    #{discountInUsd} AS discount_in_usd,
                    #{unitPriceInUsd} AS unit_price_in_usd,
                    #{language, jdbcType=VARCHAR} AS language,
                    #{clickIpAddress, jdbcType=VARCHAR} AS click_ip_address
                FROM dual
            ) slts
            WHERE
                1 = 1
            <if test = "!duplicationCut.overlapFlag and !duplicationCut.verifyCutFlag">
                AND NOT EXISTS (
                    SELECT
                        1
                    FROM
                        sales_log sl
                    WHERE
                        sl.merchant_campaign_no = slts.merchant_campaign_no
                    AND
                        sl.transaction_id = slts.transaction_id
                )
            </if>
            <if test = "!duplicationCut.overlapFlag and duplicationCut.verifyCutFlag">
                AND NOT EXISTS (
                    SELECT
                        1
                    FROM
                        sales_log sl
                    WHERE
                        sl.merchant_campaign_no = slts.merchant_campaign_no
                    AND
                        SUBSTR(sl.transaction_id, 21) = SUBSTR(slts.transaction_id, 21)
                    <if test = "duplicationCut.verifyCutTarget == 0">
                    AND
                        TRUNC(sl.sales_date, 'DD') = TRUNC(#{conversionTime, jdbcType=DATE}, 'DD')
                    </if>
                    AND
                        sl.result_id = slts.result_id
                    AND
                        (#{duplicationCut.verifyCutCondition} = 1
                            OR NVL(sl.goods_id, -1) = NVL(slts.goods_id, -1))
                )
            </if>
            <if test = "duplicationCut.overlapFlag">
                AND NOT EXISTS (
                    SELECT
                        1
                    FROM
                        sales_log sl
                    WHERE
                        sl.merchant_campaign_no = slts.merchant_campaign_no
                    AND
                        sl.uuid = slts.uuid
                    AND
                        sl.result_id = slts.result_id
                    AND
                        slts.uuid IS NOT NULL
                )
                AND NOT EXISTS (
                    SELECT
                        1
                    FROM
                        sales_log sl
                    WHERE
                        sl.merchant_campaign_no = slts.merchant_campaign_no
                    AND
                        sl.verify = slts.verify
                    AND
                        TRUNC(sl.sales_date, 'DD') = TRUNC(#{conversionTime, jdbcType=DATE}, 'DD')
                    AND
                        sl.result_id = slts.result_id
                    AND
                        sl.goods_id IS NULL
                )
            </if>
     */
    @Multiline String INSERT_CONVERSION = "";

    /**
     * Inserts the conversion log by the given {@link ConversionInsertion}.
     *
     * @param conversion
     *          the details of the conversion
     * @return the number of inserted rows
     */
    @Insert(START_TAG_SCRIPT + INSERT_CONVERSION + END_TAG_SCRIPT)
    int insert(ConversionInsertion conversion);

    /**
        SELECT
            mc.overlap_flg,
            mcs.verify_cut_flag,
            NVL(mcs.verify_cut_target, 0) AS verify_cut_target,
            NVL(mcs.verify_cut_condition, 0) AS verify_cut_condition
        FROM (
            SELECT
                #{campaignId} AS merchant_campaign_no,
                #{siteId} AS partner_site_no
            FROM
                dual
        ) slts
        INNER JOIN
            merchant_campaign mc
        ON
            mc.campaign_no = slts.merchant_campaign_no
        AND
            (mc.campaign_state_id = 1 OR slts.partner_site_no <= 10)
        INNER JOIN
            merchant_campaign_setting mcs
        ON
            slts.merchant_campaign_no = mcs.campaign_no
        INNER JOIN
            merchant_account ma
        ON
            ma.account_no  = mc.account_no
     */
    @Multiline String SELECT_DUPLICATION_CUT = "";

    /**
     * Returns the duplication cut information by the given campaign ID and site ID.
     *
     * @param campaignId
     *          ID by the given campaign
     * @param siteId
     *          ID by the given site
     * @return the duplication cut information by the given campaign ID and site ID
     */
    @Select(SELECT_DUPLICATION_CUT)
    @ConstructorArgs({ @Arg(column = "overlap_flg", javaType = boolean.class),
            @Arg(column = "verify_cut_flag", javaType = boolean.class),
            @Arg(column = "verify_cut_target", javaType = Integer.class),
            @Arg(column = "verify_cut_condition", javaType = Integer.class), })
    DuplicationCut findDuplicationCutBy(@Param("campaignId") long campaignId,
            @Param("siteId") long siteId);
}
