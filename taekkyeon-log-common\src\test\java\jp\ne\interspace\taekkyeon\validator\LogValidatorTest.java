/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.validator;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

/**
 * Unit test for {@link LogValidator}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class LogValidatorTest {

    private static final int MAX_BYTE_COUNT = 10;
    private static final String FIELD_NAME = "fieldName";

    @InjectMocks
    private LogValidator underTest;

    @Test
    public void testValidateMaxByteCountOfShouldNotThrowTaekkyeonExceptionWhenTheByteLengthOfTheGivenFieldValueIsLessThanTheGivenMaxByteCount() {
        // given
        String fieldValue = "123456789";

        // when
        underTest.validateMaxByteCountOf(fieldValue, MAX_BYTE_COUNT, FIELD_NAME);
    }

    @Test
    public void testValidateMaxByteCountOfShouldNotThrowTaekkyeonExceptionWhenTheByteLengthOfTheGivenFieldValueEqualsToTheGivenMaxByteCount() {
        // given
        String fieldValue = "1234567890";

        // when
        underTest.validateMaxByteCountOf(fieldValue, MAX_BYTE_COUNT, FIELD_NAME);
    }

    @Test
    public void testValidateMaxByteCountOfShouldThrowTaekkyeonExceptionWhenTheByteLengthOfTheGivenFieldValueIsGreaterThanTheGivenMaxByteCount() {
        // given
        String fieldValue = "1234567890a";

        // when
        try {
            underTest.validateMaxByteCountOf(fieldValue, MAX_BYTE_COUNT, FIELD_NAME);
            fail();
        } catch (TaekkyeonException ex) {
            // then
            assertEquals(
                    "The parameter [fieldName] (value:[1234567890a]) is greater than 10 bytes",
                    ex.getMessage());
        }
    }
}
