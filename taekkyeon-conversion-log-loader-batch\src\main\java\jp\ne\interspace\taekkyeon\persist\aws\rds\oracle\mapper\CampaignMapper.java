/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * Mybatis mapper for handling campaign.
 *
 * <AUTHOR> Shin
 */
public interface CampaignMapper {

    /**
        SELECT
            referer_check
        FROM
            merchant_campaign
        WHERE
            campaign_no = #{campaignId}
        AND
            referer_check IS NOT NULL
     */
    @Multiline String SELECT_REFERER = "";

    /**
     * Returns the referer checker by the given {@code campaignId}.
     *
     * @param campaignId
     *          the identifiers of the given campaign
     * @return the referer checker by the given {@code campaignId}
     * @see #SELECT_REFERER
     */
    @Select(SELECT_REFERER)
    String findRefererCheckerBy(long campaignId);
}
