/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.factory;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.Conversion;
import jp.ne.interspace.taekkyeon.model.DeviceOs;
import jp.ne.interspace.taekkyeon.model.DeviceType;
import jp.ne.interspace.taekkyeon.model.LogItem;
import jp.ne.interspace.taekkyeon.service.AdvancedLogItemParserService;
import jp.ne.interspace.taekkyeon.service.ConversionRequestParserService;
import jp.ne.interspace.taekkyeon.service.ConversionService;
import jp.ne.interspace.taekkyeon.validator.ConversionValidator;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link ConversionFactory}.
 *
 * <AUTHOR> Shin
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class ConversionFactoryTest {

    private static final long QUANTITY_VALUE_0 = 0L;
    private static final long QUANTITY_VALUE_1 = 1L;
    private static final long QUANTITY_VALUE_2 = 2L;

    private static final int INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID = 3;
    private static final int INDIVIDUAL_PURCHASE_PRODUCT_CATEGORY_RESULT_ID = 30;

    @Spy
    @InjectMocks
    private ConversionFactory underTest;

    @Mock
    private AdvancedLogItemParserService parser;

    @Mock
    private ConversionRequestParserService requestParser;

    @Mock
    private ConversionValidator conversionValidator;

    @Mock
    private ConversionService conversionService;

    @Test
    public void testCreateFromShouldReturnCorrectConversionWhenCalled() {
        // given
        String rawLogItem = "rawLogItem";
        Map<String, String> parsedLogItem = new HashMap<>();

        when(parser.parse(rawLogItem)).thenReturn(parsedLogItem);

        LocalDateTime logDate = LocalDateTime.of(2017, 12, 6, 19, 1, 42);
        long creativeId = 1;
        long siteId = 2;
        long campaignId = 3;
        DeviceType deviceType = DeviceType.ANDROID;
        DeviceOs deviceOs = DeviceOs.ANDROID;
        String ipAddress = "ipAddress";
        String referer = "referer";
        String userAgent = "userAgent";
        LocalDateTime clickDate = LocalDateTime.of(2017, 12, 6, 15, 0, 10);
        LocalDateTime conversionDate = LocalDateTime.of(2017, 12, 6, 19, 2, 25);
        String identifier = "identifier";
        String sessionId = "sessionId";
        String uuid = "uuid";
        long quantity = 5;
        String productId = "productId";
        String categoryId = "categoryId";
        BigDecimal discount = BigDecimal.valueOf(10);
        BigDecimal unitPrice = BigDecimal.valueOf(30);
        BigDecimal originalTotalPrice = BigDecimal.valueOf(35);
        String pointbackId = "pointbackId";
        String clickReferer = "clickReferer";
        String clickUrl = "clickUrl";
        String clickUserAgent = "clickUserAgent";
        Map<String, String> conversionParameters = new HashMap<>();
        Map<String, String> clickParameters = new HashMap<>();
        String transactionId1 = "2017-12-06 19:02:25-identifier-3-productId-0";
        String transactionId2 = "2017-12-06 19:02:25-identifier-3-productId-1";
        List<String> transactionIds = Arrays.asList(transactionId1, transactionId2);
        String language = "language";
        String currency = "JPY";

        String request = "request";
        Map<String, String> parsedRequest = new HashMap<>();
        int resultId = 3;
        BigDecimal price = BigDecimal.valueOf(15);
        BigDecimal transactionAmount = null;
        String customerType = "customerType";
        String clickIpAddress = "clickIpAddress";

        when(parser.getLogDateFrom(parsedLogItem)).thenReturn(logDate);
        when(parser.getCreativeIdFrom(parsedLogItem)).thenReturn(creativeId);
        when(parser.getSiteIdFrom(parsedLogItem)).thenReturn(siteId);
        when(parser.getCampaignIdFrom(parsedLogItem)).thenReturn(campaignId);
        when(parser.getDeviceTypeFrom(parsedLogItem)).thenReturn(deviceType);
        when(parser.getIpAddressFrom(parsedLogItem)).thenReturn(ipAddress);
        when(parser.getRefererFrom(parsedLogItem)).thenReturn(referer);
        when(parser.getUserAgentFrom(parsedLogItem)).thenReturn(userAgent);
        when(parser.getDeviceOsFrom(userAgent)).thenReturn(deviceOs);
        when(parser.getClickDateFrom(parsedLogItem)).thenReturn(clickDate);
        when(parser.getConversionDateFrom(parsedLogItem)).thenReturn(conversionDate);
        when(parser.getIdentifierFrom(parsedLogItem)).thenReturn(identifier);
        when(parser.getSessionIdFrom(parsedLogItem)).thenReturn(sessionId);
        when(parser.getUuidFrom(parsedLogItem)).thenReturn(uuid);
        when(parser.getQuantityFrom(parsedLogItem, resultId)).thenReturn(quantity);
        when(parser.getProductIdFrom(parsedLogItem)).thenReturn(productId);
        when(parser.getCategoryIdFrom(parsedLogItem, resultId)).thenReturn(categoryId);
        when(parser.getDiscountFrom(parsedLogItem)).thenReturn(discount);
        when(parser.getUnitPriceFrom(parsedLogItem, resultId)).thenReturn(unitPrice);
        when(parser.getOriginalTotalPriceFrom(parsedLogItem))
                .thenReturn(originalTotalPrice);
        when(parser.getPointbackIdFrom(parsedLogItem)).thenReturn(pointbackId);
        when(parser.getClickRefererFrom(parsedLogItem)).thenReturn(clickReferer);
        when(parser.getClickUrlFrom(parsedLogItem)).thenReturn(clickUrl);
        when(parser.getClickUserAgentFrom(parsedLogItem)).thenReturn(clickUserAgent);
        when(parser.getConversionParametersFrom(parsedLogItem))
                .thenReturn(conversionParameters);
        when(parser.getClickParametersFrom(parsedLogItem)).thenReturn(clickParameters);
        when(parser.getRequestFrom(parsedLogItem)).thenReturn(request);
        when(parser.getCustomerTypeFrom(parsedRequest)).thenReturn(customerType);
        when(requestParser.parseRequest(request)).thenReturn(parsedRequest);
        when(requestParser.getResultIdFrom(parsedRequest)).thenReturn(resultId);
        when(parser.getTransactionAmountFrom(parsedLogItem))
                .thenReturn(transactionAmount);
        when(requestParser.getPriceFrom(parsedRequest)).thenReturn(price);
        doReturn(price).when(underTest).getPriceFrom(transactionAmount, price);
        doReturn(originalTotalPrice).when(underTest).getOriginalTotalPrice(
                originalTotalPrice, price, unitPrice, resultId);
        when(conversionService
                .generateTransactionIdsFrom(quantity, conversionDate, identifier,
                        productId, resultId, campaignId, customerType))
                .thenReturn(transactionIds);
        when(parser.getLanguageFrom(parsedRequest)).thenReturn(language);
        when(parser.getClickIpAddressFrom(parsedLogItem)).thenReturn(clickIpAddress);
        doReturn(clickIpAddress).when(underTest).getClientIpFrom(clickIpAddress);
        when(parser.getCurrencyFrom(parsedLogItem)).thenReturn(currency);

        // when
        LogItem actual = underTest.createFrom(rawLogItem);

        // then
        assertTrue(actual instanceof Conversion);

        Conversion actualConversion = (Conversion) actual;
        assertEquals(logDate, actualConversion.getLogDate());
        assertEquals(creativeId, actualConversion.getCreativeId());
        assertEquals(siteId, actualConversion.getSiteId());
        assertEquals(campaignId, actualConversion.getCampaignId());
        assertEquals(deviceType, actualConversion.getDeviceType());
        assertEquals(ipAddress, actualConversion.getIpAddress());
        assertEquals(referer, actualConversion.getReferer());
        assertEquals(userAgent, actualConversion.getUserAgent());
        assertEquals(deviceOs, actualConversion.getDeviceOs());
        assertEquals(clickDate, actualConversion.getClickDate());
        assertEquals(conversionDate, actualConversion.getConversionDate());
        assertEquals(identifier, actualConversion.getIdentifier());
        assertEquals(sessionId, actualConversion.getSessionId());
        assertEquals(uuid, actualConversion.getUuid());
        assertEquals(quantity, actualConversion.getQuantity());
        assertEquals(productId, actualConversion.getProductId());
        assertEquals(categoryId, actualConversion.getCategoryId());
        assertEquals(discount, actualConversion.getDiscount());
        assertEquals(unitPrice, actualConversion.getUnitPrice());
        assertEquals(originalTotalPrice, actualConversion.getOriginalTotalPrice());
        assertEquals(pointbackId, actualConversion.getPointbackId());
        assertEquals(clickReferer, actualConversion.getClickReferer());
        assertEquals(clickUrl, actualConversion.getClickUrl());
        assertEquals(clickUserAgent, actualConversion.getClickUserAgent());
        assertEquals(conversionParameters, actualConversion.getConversionParameters());
        assertEquals(clickParameters, actualConversion.getClickParameters());
        assertEquals(resultId, actualConversion.getResultId());
        assertEquals(price, actualConversion.getPrice());
        assertEquals(transactionIds, actualConversion.getTransactionIds());
        assertEquals(customerType, actualConversion.getCustomerType());
        assertEquals(language, actualConversion.getLanguage());
        assertEquals(clickIpAddress, actualConversion.getClickIpAddress());
        assertEquals(currency, actualConversion.getCurrency());

        verify(parser).getTransactionAmountFrom(parsedLogItem);
        verify(conversionValidator).validateResultId(resultId, campaignId);
        verify(underTest).getPriceFrom(transactionAmount, price);
    }

    @Test
    public void testIsMultipleProductConversionShouldReturnFalseWhenGivenAResultIdThatIsNotCategoryResultIdAndProcudtResultIdAndAQuantityLessThanOne() {
        // given
        int resultId = 4;

        // when
        boolean actual = underTest.isMultipleProductConversion(resultId,
                QUANTITY_VALUE_0);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsMultipleProductConversionShouldReturnFalseWhenGivenAResultIdThatIsNotCategoryResultIdAndProcudtResultIdAndAQuantityEqualToOne() {
        // given
        int resultId = 31;

        // when
        boolean actual = underTest.isMultipleProductConversion(resultId,
                QUANTITY_VALUE_1);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsMultipleProductConversionShouldReturnFalseWhenGivenAResultIdThatIsNotCategoryResultIdAndProcudtResultIdAndAQuantityGreaterThanOne() {
        // given
        int resultId = 2;

        // when
        boolean actual = underTest.isMultipleProductConversion(resultId,
                QUANTITY_VALUE_2);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsMultipleProductConversionShouldReturnFalseWhenGivenAProductResultIdAndAQuantityLessThanOne() {
        // when
        boolean actual = underTest.isMultipleProductConversion(
                INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID, QUANTITY_VALUE_0);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsMultipleProductConversionShouldReturnFalseWhenGivenACategoryResultIdAndAQuantityLessThanOne() {
        // when
        boolean actual = underTest.isMultipleProductConversion(
                INDIVIDUAL_PURCHASE_PRODUCT_CATEGORY_RESULT_ID, QUANTITY_VALUE_0);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsMultipleProductConversionShouldReturnFalseWhenGivenAProductResultIdAndAQuantityEqualToOne() {
        // when
        boolean actual = underTest.isMultipleProductConversion(
                INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID, QUANTITY_VALUE_1);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsMultipleProductConversionShouldReturnFalseWhenGivenACategoryResultIdAndAQuantityEqualToOne() {
        // when
        boolean actual = underTest.isMultipleProductConversion(
                INDIVIDUAL_PURCHASE_PRODUCT_CATEGORY_RESULT_ID, QUANTITY_VALUE_1);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsMultipleProductConversionShouldReturnTrueWhenGivenAProductResultIdAndAQuantityGreaterThanOne() {
        // when
        boolean actual = underTest.isMultipleProductConversion(
                INDIVIDUAL_PURCHASE_PRODUCT_RESULT_ID, QUANTITY_VALUE_2);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsMultipleProductConversionShouldReturnTrueWhenGivenACategoryResultIdAndAQuantityGreaterThanOne() {
        // when
        boolean actual = underTest.isMultipleProductConversion(
                INDIVIDUAL_PURCHASE_PRODUCT_CATEGORY_RESULT_ID, QUANTITY_VALUE_2);

        // then
        assertTrue(actual);
    }

    @Test
    public void testGetPriceFromShouldReturnCorrectPriceWhenTransactionAmountIsNotNull() {
        // given
        BigDecimal expectedPrice = BigDecimal.valueOf(15);

        // when
        BigDecimal actual = underTest.getPriceFrom(expectedPrice, BigDecimal.ZERO);

        // then
        assertNotNull(actual);
        assertEquals(expectedPrice, actual);
    }

    @Test
    public void testGetPriceFromShouldReturnCorrectPriceWhenTransactionAmountIsNull() {
        // given
        BigDecimal expectedPrice = BigDecimal.valueOf(15);

        // when
        BigDecimal actual = underTest.getPriceFrom(null, expectedPrice);

        // then
        assertNotNull(actual);
        assertEquals(expectedPrice, actual);
    }

    @Test
    public void testGetPriceFromShouldReturnCorrectPriceWhenTransactionAmountIsNullAndInputPriceIsNegative() {
        // given
        BigDecimal inputPrice = BigDecimal.valueOf(-15);
        BigDecimal expectedPrice = BigDecimal.ZERO;

        // when
        BigDecimal actual = underTest.getPriceFrom(null, inputPrice);

        // then
        assertNotNull(actual);
        assertEquals(expectedPrice, actual);
    }

    @Test
    public void testGetOriginalTotalPriceShouldReturnCorrectOriginalTotalPriceWhenUnitPriceIsZeroAndResultIdIsThree() {
        // given
        BigDecimal originalTotalPrice = BigDecimal.valueOf(3);
        BigDecimal price = BigDecimal.valueOf(15);
        BigDecimal unitPrice = BigDecimal.ZERO;
        int resultId = 3;

        // when
        BigDecimal actual = underTest
                .getOriginalTotalPrice(originalTotalPrice, price, unitPrice, resultId);

        // then
        assertNotNull(actual);
        assertEquals(BigDecimal.ZERO, actual);
    }

    @Test
    public void testGetOriginalTotalPriceShouldReturnCorrectOriginalTotalPriceWhenUnitPriceIsZeroAndResultIdIsThirty() {
        // given
        BigDecimal originalTotalPrice = BigDecimal.valueOf(3);
        BigDecimal price = BigDecimal.valueOf(15);
        BigDecimal unitPrice = BigDecimal.ZERO;
        int resultId = 30;

        // when
        BigDecimal actual = underTest
                .getOriginalTotalPrice(originalTotalPrice, price, unitPrice, resultId);

        // then
        assertNotNull(actual);
        assertEquals(BigDecimal.ZERO, actual);
    }

    @Test
    public void testGetOriginalTotalPriceShouldReturnCorrectOriginalTotalPriceWhenUnitPriceIsZeroAndResultIdIsNotProductOrCategoryReward() {
        // given
        BigDecimal originalTotalPrice = BigDecimal.valueOf(3);
        BigDecimal price = BigDecimal.valueOf(15);
        BigDecimal unitPrice = BigDecimal.ZERO;
        int resultId = 31;

        // when
        BigDecimal actual = underTest
                .getOriginalTotalPrice(originalTotalPrice, price, unitPrice, resultId);

        // then
        assertNotNull(actual);
        assertEquals(originalTotalPrice, actual);
    }

    @Test
    public void testGetOriginalTotalPriceShouldReturnCorrectOriginalTotalPriceWhenOriginalTotalPriceIsNotZero() {
        // given
        BigDecimal originalTotalPrice = BigDecimal.valueOf(3);
        BigDecimal price = BigDecimal.valueOf(15);
        BigDecimal unitPrice = BigDecimal.valueOf(5);
        int resultId = 30;

        // when
        BigDecimal actual = underTest
                .getOriginalTotalPrice(originalTotalPrice, price, unitPrice, resultId);

        // then
        assertNotNull(actual);
        assertEquals(originalTotalPrice, actual);
    }

    @Test
    public void testGetOriginalTotalPriceShouldReturnCorrectPriceWhenOriginalTotalPriceIsZero() {
        // given
        BigDecimal price = BigDecimal.valueOf(15);
        BigDecimal unitPrice = BigDecimal.valueOf(5);
        int resultId = 30;

        // when
        BigDecimal actual = underTest
                .getOriginalTotalPrice(BigDecimal.ZERO, price, unitPrice, resultId);

        // then
        assertNotNull(actual);
        assertEquals(price, actual);
    }

    @Test
    public void testGetOriginalTotalPriceShouldReturnCorrectPriceWhenUnitPriceIsZeroAndResultIdIsThirty() {
        // given
        BigDecimal price = BigDecimal.valueOf(15);
        BigDecimal unitPrice = BigDecimal.ZERO;
        BigDecimal totalPrice = BigDecimal.valueOf(14);
        int resultId = 30;

        // when
        BigDecimal actual = underTest
                .getOriginalTotalPrice(totalPrice, price, unitPrice, resultId);

        // then
        assertNotNull(actual);
        assertEquals(unitPrice, actual);
    }

    @Test
    public void testGetOriginalTotalPriceShouldReturnCorrectPriceWhenUnitPriceIsZeroAndResultIdIsThree() {
        // given
        BigDecimal price = BigDecimal.valueOf(15);
        BigDecimal totalPrice = BigDecimal.valueOf(14);
        BigDecimal unitPrice = BigDecimal.ZERO;
        int resultId = 3;

        // when
        BigDecimal actual = underTest
                .getOriginalTotalPrice(totalPrice, price, unitPrice, resultId);

        // then
        assertNotNull(actual);
        assertEquals(unitPrice, actual);
    }

    @Test
    public void testGetOriginalTotalPriceShouldReturnCorrectPriceWhenUnitPriceIsNotZeroAndResultIdIsThree() {
        // given
        BigDecimal price = BigDecimal.valueOf(15);
        BigDecimal totalPrice = BigDecimal.valueOf(14);
        BigDecimal unitPrice = BigDecimal.valueOf(5);
        int resultId = 3;

        // when
        BigDecimal actual = underTest
                .getOriginalTotalPrice(totalPrice, price, unitPrice, resultId);

        // then
        assertNotNull(actual);
        assertEquals(totalPrice, actual);
    }

    @Test
    public void testGetClientIpFromShouldReturnCorrectValueWhenIpAddressIsCommaSeparated() {
        // given
        String ipAddress = "*******, *******";
        String expected = "*******";

        // when
        String actual = underTest.getClientIpFrom(ipAddress);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetClientIpFromShouldReturnCorrectValueWhenIpAddressIsNotCommaSeparated() {
        // given
        String ipAddress = "*******";

        // when
        String actual = underTest.getClientIpFrom(ipAddress);

        // then
        assertEquals(ipAddress, actual);
    }
}
