/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.YearMonth;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.CurrencyRate;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * Integration test for {@link CurrencyMapper}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class CurrencyMapperTest {

    @Inject
    private CurrencyMapper underTest;

    @Test
    public void testFindCampaignCurrencyRateShouldReturnCorrectDataWhenMerchantCountryQuoteCurrencyAndCampaignCurrencyExists() {
        // when
        CurrencyRate actual = underTest.findCampaignCurrencyRate(1,
                YearMonth.of(2017, 1));

        // then
        assertCurrencyRate(actual, "USD", new BigDecimal("1.**********"));
    }

    @Test
    public void testFindCampaignCurrencyRateShouldReturnOneWhenMerchantCountryQuoteCurrencyAndCampaignCurrencyDoNotExist() {
        // when
        CurrencyRate actual = underTest.findCampaignCurrencyRate(2,
                YearMonth.of(2017, 1));

        // then
        assertCurrencyRate(actual, "IDR", new BigDecimal("1.**********"));
    }

    @Test
    public void testFindCurrencyRateShouldReturnCorrectCurrencyRateWhenCurrencyExists() {
        // given
        String currency = "JPY";

        // when
        CurrencyRate actual = underTest.findCurrencyRate(currency, 3,
                YearMonth.of(2021, 6));

        // then
        assertCurrencyRate(actual, currency, new BigDecimal("123.4500000000"));
    }

    @Test
    public void testFindCurrencyRateShouldReturnCorrectCurrencyRateWhenCurrencyDoesNotExist() {
        // given
        String currency = "SGD";

        // when
        CurrencyRate actual = underTest.findCurrencyRate(currency, 3,
                YearMonth.of(2021, 6));

        // then
        assertCurrencyRate(actual, null, new BigDecimal("1.**********"));
    }

    private void assertCurrencyRate(CurrencyRate actual, String expectedCurrency,
            BigDecimal expectedRate) {
        assertNotNull(actual);
        assertEquals(expectedCurrency, actual.getCurrency());
        assertEquals(expectedRate, actual.getRate());
    }
}
