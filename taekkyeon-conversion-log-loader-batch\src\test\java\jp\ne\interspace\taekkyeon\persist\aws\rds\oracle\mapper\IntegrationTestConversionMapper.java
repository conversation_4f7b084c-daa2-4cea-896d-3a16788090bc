/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.model.CommissionType;
import jp.ne.interspace.taekkyeon.model.ConversionStatus;
import jp.ne.interspace.taekkyeon.model.DeviceOs;
import jp.ne.interspace.taekkyeon.model.DeviceType;
import jp.ne.interspace.taekkyeon.model.IntegrationTestConversion;
import jp.ne.interspace.taekkyeon.model.RewardType;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * MyBatis mapper for conversions to be used in the integration tests.
 *
 * <AUTHOR> Shin
 */
public interface IntegrationTestConversionMapper {

    /**
        SELECT
            seq_no conversionId,
            banner_id creativeId,
            merchant_campaign_no campaignId,
            click_date clickDate,
            sales_date conversionDate,
            log_date logDate,
            confirmed_date confirmedDate,
            transaction_id transactionId,
            partner_site_no siteId,
            rank rank,
            verify identifier,
            result_id resultId,
            goods_id productId,
            sales_log_status conversionStatus,
            sales_count quantity,
            price,
            total_price totalPrice,
            reward_type rewardType,
            sales_reward reward,
            total_price_reward totalPriceReward,
            commission_type commissionType,
            at_commission atCommission,
            agent_commission agentCommission,
            p_agent_commission publisherAgentCommission,
            ip ipAddress,
            referer,
            user_agent userAgent,
            reward_edit_date rewardEditDate,
            tracking_type trackingType,
            default_sales_count defaultQuantity,
            default_price defaultPrice,
            default_result_id defaultResultId,
            lp_url lpUrl,
            device_type deviceType,
            pointback_id pointbackId,
            pb_id_duplicative_flag pbIdDuplicativeFlag,
            session_id sessionId,
            uuid,
            device_os deviceOs,
            category_id categoryId,
            discount,
            internal_transaction_id internalTransactionId,
            original_currency_total_price originalCurrencyTotalPrice,
            original_currency originalCurrency,
            click_referer clickReferer,
            click_url clickUrl,
            click_user_agent clickUserAgent,
            customer_type customerType,
            language,
            click_ip clickIpAddress
        FROM
            sales_log
     */
    @Multiline String SELECT_CONVERSIONS = "";

    /**
        ORDER BY
            transaction_id
     */
    @Multiline String ORDER_BY_TRANSACTION_ID = "";

    /**
     * Returns the {@link IntegrationTestConversion}s sorted by transaction ID.
     *
     * @return the {@link IntegrationTestConversion}s sorted by transaction ID
     */
    @Select(SELECT_CONVERSIONS + ORDER_BY_TRANSACTION_ID)
    @ConstructorArgs({ @Arg(column = "conversionId", javaType = Long.class),
            @Arg(column = "creativeId", javaType = Long.class),
            @Arg(column = "campaignId", javaType = Long.class),
            @Arg(column = "clickDate", javaType = LocalDateTime.class),
            @Arg(column = "conversionDate", javaType = LocalDateTime.class),
            @Arg(column = "logDate", javaType = LocalDateTime.class),
            @Arg(column = "confirmedDate", javaType = LocalDateTime.class),
            @Arg(column = "transactionId", javaType = String.class),
            @Arg(column = "siteId", javaType = Long.class),
            @Arg(column = "rank", javaType = Integer.class),
            @Arg(column = "identifier", javaType = String.class),
            @Arg(column = "resultId", javaType = Integer.class),
            @Arg(column = "productId", javaType = String.class),
            @Arg(column = "conversionStatus", javaType = ConversionStatus.class),
            @Arg(column = "quantity", javaType = Long.class),
            @Arg(column = "price", javaType = BigDecimal.class),
            @Arg(column = "totalPrice", javaType = BigDecimal.class),
            @Arg(column = "rewardType", javaType = RewardType.class),
            @Arg(column = "reward", javaType = BigDecimal.class),
            @Arg(column = "totalPriceReward", javaType = BigDecimal.class),
            @Arg(column = "commissionType", javaType = CommissionType.class),
            @Arg(column = "atCommission", javaType = BigDecimal.class),
            @Arg(column = "agentCommission", javaType = BigDecimal.class),
            @Arg(column = "publisherAgentCommission", javaType = BigDecimal.class),
            @Arg(column = "ipAddress", javaType = String.class),
            @Arg(column = "referer", javaType = String.class),
            @Arg(column = "userAgent", javaType = String.class),
            @Arg(column = "rewardEditDate", javaType = LocalDateTime.class),
            @Arg(column = "trackingType", javaType = Integer.class),
            @Arg(column = "defaultQuantity", javaType = Long.class),
            @Arg(column = "defaultPrice", javaType = BigDecimal.class),
            @Arg(column = "defaultResultId", javaType = Integer.class),
            @Arg(column = "lpUrl", javaType = String.class),
            @Arg(column = "deviceType", javaType = DeviceType.class),
            @Arg(column = "pointbackId", javaType = String.class),
            @Arg(column = "pbIdDuplicativeFlag", javaType = Integer.class),
            @Arg(column = "sessionId", javaType = String.class),
            @Arg(column = "uuid", javaType = String.class),
            @Arg(column = "deviceOs", javaType = DeviceOs.class),
            @Arg(column = "categoryId", javaType = String.class),
            @Arg(column = "discount", javaType = BigDecimal.class),
            @Arg(column = "internalTransactionId", javaType = String.class),
            @Arg(column = "originalCurrencyTotalPrice", javaType = BigDecimal.class),
            @Arg(column = "originalCurrency", javaType = String.class),
            @Arg(column = "clickReferer", javaType = String.class),
            @Arg(column = "clickUrl", javaType = String.class),
            @Arg(column = "clickUserAgent", javaType = String.class),
            @Arg(column = "customerType", javaType = String.class),
            @Arg(column = "language", javaType = String.class),
            @Arg(column = "clickIpAddress", javaType = String.class) })
    List<IntegrationTestConversion> findAll();

    /**
        DELETE FROM
            sales_log
     */
    @Multiline String DELETE_ALL = "";

    /**
     * Deletes all conversions.
     */
    @Delete(DELETE_ALL)
    void deleteAll();
}
