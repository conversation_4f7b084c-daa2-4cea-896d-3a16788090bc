/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import javax.inject.Singleton;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;

import redis.embedded.RedisServer;

import jp.ne.interspace.taekkyeon.factory.ConversionFactory;
import jp.ne.interspace.taekkyeon.factory.LogItemFactory;
import jp.ne.interspace.taekkyeon.loader.ConversionLoader;
import jp.ne.interspace.taekkyeon.loader.LogItemLoader;
import jp.ne.interspace.taekkyeon.service.BudgetCapCalculatorService;
import jp.ne.interspace.taekkyeon.service.RedisProductCategoryBudgetCapCalculatorService;
import jp.ne.interspace.taekkyeon.service.RedisTransactionBudgetCapCalculatorService;

/**
 * Guice module for the conversion log loader batch.
 *
 * <AUTHOR>
 */
public class ConversionLogLoaderJunitModule extends AbstractModule {

    private static final int REDIS_PORT = 6379;

    @Override
    protected void configure() {
        System.setProperty("isBatchTransaction", "false");

        install(new CommonLogLoaderModuleForNewRefactoring());

        bind(LogItemFactory.class).to(ConversionFactory.class);
        bind(LogItemLoader.class).to(ConversionLoader.class);
        bind(BudgetCapCalculatorService.class)
                .annotatedWith(TransactionBudgetCapCalculatorServiceBinding.class)
                .to(RedisTransactionBudgetCapCalculatorService.class);
        bind(BudgetCapCalculatorService.class)
                .annotatedWith(ProductCategoryBudgetCapCalculatorServiceBinding.class)
                .to(RedisProductCategoryBudgetCapCalculatorService.class);
    }

    @Provides @Singleton
    private RedisServer provideRedisServer() throws Exception {
        return new RedisServer(REDIS_PORT);
    }
}
