/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.CampaignSettingDuplicationCutDetails;

import static jp.ne.interspace.taekkyeon.model.DuplicationCutTarget.PERMANENT;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

/**
 * Integration test for {@link CampaignSettingsMapper}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class CampaignSettingsMapperTest {

    @Inject
    private CampaignSettingsMapper underTest;

    @Test
    public void testFindDuplicationCutDetailsByShouldReturnCorrectDataWhenCalled() {
        // given
        long campaignId = 1;

        // when
        CampaignSettingDuplicationCutDetails actual = underTest
                .findDuplicationCutDetailsBy(campaignId);

        // then
        assertTrue(actual.isEnabled());
        assertEquals(PERMANENT, actual.getTarget());
    }

}
