/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * Base class for the various log items.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor @ToString
public class LogItem {

    private final LocalDateTime logDate;
    private final long creativeId;
    private final long siteId;
    private final long campaignId;
    private final DeviceType deviceType;
    private final DeviceOs deviceOs;
    private final String ipAddress;
    private final String referer;
    private final String userAgent;
}
