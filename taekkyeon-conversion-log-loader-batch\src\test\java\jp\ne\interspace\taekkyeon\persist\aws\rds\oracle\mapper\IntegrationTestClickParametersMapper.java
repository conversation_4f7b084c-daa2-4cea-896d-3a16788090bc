/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.model.IntegrationTestParameter;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * MyBatis mapper for click parameters to be used in the integration tests.
 *
 * <AUTHOR> Varga
 */
public interface IntegrationTestClickParametersMapper {

    /**
        SELECT
            param_name name,
            param_value value
        FROM
            click_parameters
     */
    @Multiline String SELECT_ALL_PARAMETERS = "";

    /**
     * Returns all click parameters.
     *
     * @return all click parameters
     */
    @Select(SELECT_ALL_PARAMETERS)
    List<IntegrationTestParameter> findAll();

    /**
        DELETE FROM
            click_parameters
     */
    @Multiline String DELETE_ALL = "";

    /**
     * Deletes all click parameters.
     */
    @Delete(DELETE_ALL)
    void deleteAll();
}
