/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.model.PublisherAgencyCommissionPolicy;
import jp.ne.interspace.taekkyeon.model.PublisherData;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * MyBatis mapper for handling publisher account database operations.
 *
 * <AUTHOR> Shin
 */
public interface PublisherAccountMapper {

    /**
        SELECT
            pa.country_code AS countryCode,
            NVL(pag.commission_policy, 0) AS publisherAgencyPolicy
        FROM
            partner_account pa
        INNER JOIN
            partner_site ps
        ON
            pa.account_no = ps.account_no
        LEFT JOIN
            publisher_agency pag
        ON
            pa.agency_id = pag.id
        WHERE
            ps.site_no = #{siteId}
     */
    @Multiline String SELECT_PUBLISHER_DATA = "";

    /**
     * Returns the publisher data for conversion reward calculation.
     *
     * @param siteId
     *          ID by the given site
     * @return the publisher data for conversion reward calculation
     */
    @Select(SELECT_PUBLISHER_DATA)
    @ConstructorArgs({ @Arg(column = "countryCode", javaType = String.class),
            @Arg(column = "publisherAgencyPolicy",
                    javaType = PublisherAgencyCommissionPolicy.class) })
    PublisherData findPublisherDataBy(long siteId);
}
