/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * Mybat<PERSON> mapper for handling result target setting data of campaign.
 *
 * <AUTHOR>
 */
public interface ResultTargetSettingMapper {

    /**
        SELECT
            COUNT(*)
        FROM
            result_target_setting
        WHERE
            merchant_campaign_no = #{campaignId}
        AND
            result_id = #{resultId}
        AND
            use_flag = 1
    */
    @Multiline String COUNT_RESULT_ID_BY_CAMPAIGN_ID = "";

    /**
     * Returns {@code true}, when result id is found for given campaign id
     * otherwise return {@code false}.
     *
     * @param resultId
     *            result id to be validated
     * @param campaignId
     *            campaign id to be used in validation
     * @return {@code true} if the given result id to be used for validation
     *            exists in the database otherwise return {@code false}
     * @see #COUNT_RESULT_ID_BY_CAMPAIGN_ID
     */
    @Select(COUNT_RESULT_ID_BY_CAMPAIGN_ID)
    boolean isResultIdAvailable(@Param("resultId") int resultId,
            @Param("campaignId") long campaignId);
}
