/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.factory;

import java.time.LocalDateTime;
import java.util.Map;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.Click;
import jp.ne.interspace.taekkyeon.model.DeviceOs;
import jp.ne.interspace.taekkyeon.model.DeviceType;
import jp.ne.interspace.taekkyeon.model.LogItem;
import jp.ne.interspace.taekkyeon.service.AdvancedLogItemParserService;

import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link ClickFactory}.
 *
 * <AUTHOR> Shin
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class ClickFactoryTest {

    private static final String RAW_LOG_ITEM = "rawLogItem";
    private static final LocalDateTime RAW_LOG_DATE = LocalDateTime.of(2017, 1, 1, 0, 0);
    private static final long RAW_CREATIVE_ID = 10;
    private static final long RAW_SITE_ID = 5;
    private static final long RAW_CAMPAIGN_ID = 6;
    private static final DeviceType RAW_DEVICE_TYPE = DeviceType.ANDROID;
    private static final DeviceOs RAW_DEVICE_OS = DeviceOs.ANDROID;
    private static final String RAW_IP_ADDRESS = "ipAddress";
    private static final String RAW_REFERER = "referer";
    private static final String RAW_USER_AGENT = "userAgent";
    private static final String RAW_TRACKING_ID = "trackingId";
    private static final String RAW_PRODUCT_ID = "productId";
    private static final String OPTIMIZER_UUID = "OPTIMIZER_UUID";
    private static final String LANGUAGE = "LANGUAGE";
    private static final String RAW_OPTIMIZER_UUID = "optimizerUuid";
    private static final String RAW_LANGUAGE = "language";
    private static final String RAW_CLICK_IP_ADDRESS = "clickIpAddress";

    @InjectMocks @Spy
    private ClickFactory underTest;

    @Mock
    private AdvancedLogItemParserService parser;

    @SuppressWarnings("unchecked")
    @Test
    public void testCreateFromShouldReturnCorrectDataWhenCalled() {
        // given
        Map<String, String> parsedLogItem = mock(Map.class);
        when(parser.parse(RAW_LOG_ITEM)).thenReturn(parsedLogItem);
        when(parser.getLogDateFrom(parsedLogItem)).thenReturn(RAW_LOG_DATE);
        when(parser.getCreativeIdFrom(parsedLogItem)).thenReturn(RAW_CREATIVE_ID);
        when(parser.getSiteIdFrom(parsedLogItem)).thenReturn(RAW_SITE_ID);
        when(parser.getCampaignIdFrom(parsedLogItem)).thenReturn(RAW_CAMPAIGN_ID);
        when(parser.getDeviceTypeFrom(parsedLogItem)).thenReturn(RAW_DEVICE_TYPE);
        when(parser.getIpAddressFrom(parsedLogItem)).thenReturn(RAW_IP_ADDRESS);
        when(parser.getRefererFrom(parsedLogItem)).thenReturn(RAW_REFERER);
        when(parser.getUserAgentFrom(parsedLogItem)).thenReturn(RAW_USER_AGENT);
        when(parser.getTrackingIdFrom(parsedLogItem)).thenReturn(RAW_TRACKING_ID);
        when(parser.getProductIdFrom(parsedLogItem)).thenReturn(RAW_PRODUCT_ID);
        when(parser.getDeviceOsFrom(RAW_USER_AGENT)).thenReturn(RAW_DEVICE_OS);
        when(parsedLogItem.get(OPTIMIZER_UUID)).thenReturn(RAW_OPTIMIZER_UUID);
        when(parsedLogItem.get(LANGUAGE)).thenReturn(RAW_LANGUAGE);

        // when
        LogItem actual = underTest.createFrom(RAW_LOG_ITEM);

        // then
        assertTrue(actual instanceof Click);
        Click actualClick = (Click) actual;
        assertSame(RAW_LOG_DATE, actualClick.getLogDate());
        assertSame(RAW_CREATIVE_ID, actualClick.getCreativeId());
        assertSame(RAW_SITE_ID, actualClick.getSiteId());
        assertSame(RAW_CAMPAIGN_ID, actualClick.getCampaignId());
        assertSame(RAW_DEVICE_TYPE, actualClick.getDeviceType());
        assertSame(RAW_IP_ADDRESS, actualClick.getIpAddress());
        assertSame(RAW_REFERER, actualClick.getReferer());
        assertSame(RAW_USER_AGENT, actualClick.getUserAgent());
        assertSame(RAW_TRACKING_ID, actualClick.getTrackingId());
        assertSame(RAW_PRODUCT_ID, actualClick.getProductId());
        assertSame(RAW_DEVICE_OS, actualClick.getDeviceOs());
        assertSame(RAW_OPTIMIZER_UUID, actualClick.getOptimizerUuid());
        assertSame(RAW_LANGUAGE, actualClick.getLanguage());
    }
}
