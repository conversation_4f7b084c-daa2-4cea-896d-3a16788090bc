/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * Key for finding SQS URLs.
 *
 * <AUTHOR>
 */
@AllArgsConstructor(staticName = "of") @EqualsAndHashCode
public class BaseSqsNameKey {
    private Country country;
    private Environment environment;
    private LogType logType;
}
