/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * DTO for holding to find reward setting from cache.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter @EqualsAndHashCode
public class RewardSettingKey {

    private final long campaignId;
    private final LocalDateTime time;
    private final int rank;
    private final DeviceType deviceType;
    private final int resultId;
    private final String productId;
    private final String categoryId;
    private final String customerType;
}
