/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * DTO for holding publisher data.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter @ToString
public class PublisherData {

    private final String countryCode;
    private final PublisherAgencyCommissionPolicy publisherAgencyPolicy;
}
