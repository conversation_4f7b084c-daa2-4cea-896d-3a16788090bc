/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.validator;

import javax.inject.Singleton;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.inject.Inject;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;
import jp.ne.interspace.taekkyeon.model.Pair;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ResultTargetSettingMapper;

import static java.lang.String.format;

/**
 * Class for validating a conversion.
 *
 * <AUTHOR>
 */
@Singleton
public class ConversionValidator {

    private static final String RESULT_ID_IS_NOT_FOUND = "Result Id %s is not found for campaign Id %s";

    @Inject
    private ResultTargetSettingMapper resultTargetSettingMapper;

    private LoadingCache<Pair<Integer, Long>, Boolean> resultIdCache = CacheBuilder
            .newBuilder().maximumSize(1000)
            .build(new CacheLoader<Pair<Integer, Long>, Boolean>() {

                @Override
                public Boolean load(Pair<Integer, Long> pair) {
                    boolean result = resultTargetSettingMapper.isResultIdAvailable(
                            pair.getLeft(),
                            pair.getRight());
                    return result;
                }
            });

    /**
     * If {@code resultId} of {@code campaignId} is not found , throws
     * {@link TaekkyeonException}.
     *
     * @param resultId
     *            result id to be validated
     * @param campaignId
     *            campaign id to be used in validation
     */
    public void validateResultId(int resultId, long campaignId) {
        Pair<Integer, Long> key = Pair.of(resultId, campaignId);
        if (!resultIdCache.getUnchecked(key)) {
            throw new TaekkyeonException(
                    format(RESULT_ID_IS_NOT_FOUND, resultId, campaignId));
        }
    }
}
