/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import javax.inject.Singleton;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.inject.Inject;

import jp.ne.interspace.taekkyeon.model.CampaignSettingDuplicationCutDetails;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CampaignSettingsMapper;

/**
 * Service layer for handling campaign setting data.
 *
 * <AUTHOR> Mayur
 */
@Singleton
public class CampaignSettingService {

    @Inject
    private CampaignSettingsMapper campaignSettingsMapper;

    private LoadingCache<Long, CampaignSettingDuplicationCutDetails> campaignSettingsCache
                = CacheBuilder.newBuilder().maximumSize(1000)
                .build(new CacheLoader<Long, CampaignSettingDuplicationCutDetails>() {

                    @Override
                    public CampaignSettingDuplicationCutDetails load(Long campaignId) {
                            return campaignSettingsMapper
                                    .findDuplicationCutDetailsBy(campaignId);
                    }
                });

    /**
     * Returns the duplicate cut details by the given {@code campaignId}.
     *
     * @param campaignId
     *              the given campaign ID
     * @return the duplicate cut details by the given {@code campaignId}
     */
    public CampaignSettingDuplicationCutDetails findDuplicationCutDetailsBy(long campaignId) {
        return campaignSettingsCache.getUnchecked(campaignId);
    }
}
