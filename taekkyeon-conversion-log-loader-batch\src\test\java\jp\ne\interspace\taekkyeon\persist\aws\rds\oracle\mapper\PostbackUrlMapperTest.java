/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * Integration test for {@link PostbackUrlMapper}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class PostbackUrlMapperTest {

    @Inject
    private PostbackUrlMapper underTest;

    @Test
    public void testIsPostbackAvailableForShouldReturnTrueWhenGivenInvalidCreativeWithSiteHasAllPostbackSettings() {
        // given
        long siteId = 301;

        // when
        boolean actual = underTest.isPostbackAvailableFor(siteId, 0L);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsPostbackAvailableForShouldReturnTrueWhenGivenValidCreativeWithSiteHasAllPostbackSettings() {
        // given
        long siteId = 301;

        // when
        boolean actual = underTest.isPostbackAvailableFor(siteId, 1024L);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsPostbackAvailableForShouldReturnFalseWhenSiteHasDisabledPostbackSettings() {
        // given
        long siteId = 302;

        // when
        boolean actual = underTest.isPostbackAvailableFor(siteId, 1024L);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsPostbackAvailableForShouldReturnFalseWhenGivenInvalidCreativeWithSiteHasTargetCreativePostbackSettings() {
        // given
        long siteId = 303;

        // when
        boolean actual = underTest.isPostbackAvailableFor(siteId, 0L);

        // then
        assertFalse(actual);
    }

    @Test
    public void testIsPostbackAvailableForShouldReturnTrueWhenGivenValidCreativeWithSiteHasTargetCreativePostbackSettings() {
        // given
        long siteId = 303;

        // when
        boolean actual = underTest.isPostbackAvailableFor(siteId, 1024L);

        // then
        assertTrue(actual);
    }

    @Test
    public void testIsPostbackAvailableForShouldReturnFalseWhenInvalidPostbackSettings() {
        // given
        long siteId = 304;

        // when
        boolean actual = underTest.isPostbackAvailableFor(siteId, 1024L);

        // then
        assertFalse(actual);
    }
}
