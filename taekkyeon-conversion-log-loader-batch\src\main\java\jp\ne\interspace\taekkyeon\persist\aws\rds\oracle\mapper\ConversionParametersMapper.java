/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import org.apache.ibatis.annotations.Insert;

import jp.ne.interspace.taekkyeon.model.ConversionParameter;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * Mybat<PERSON> mapper for handling conversion parameters.
 *
 * <AUTHOR> Shin
 */
public interface ConversionParametersMapper {

    /**
        INSERT INTO
            sales_log_cv_parameters (
                merchant_campaign_no,
                transaction_id,
                param_name,
                param_value,
                created_by,
                created_on
            )
        VALUES (
            #{campaignId},
            #{transactionId},
            #{paramName},
            #{paramValue, jdbcType=VARCHAR},
            'ConversionLoaderBatch',
            SYSDATE
        )
     */
    @Multiline String INSERT_CONVERSION_PARAMETERS = "";

    /**
     * Inserts the conversion parameter into the database by {@link ConversionParameter}.
     *
     * @param conversionParameter
     *          the parameter of conversion
     * @return the number of inserted rows
     * @see #INSERT_CONVERSION_PARAMETERS
     */
    @Insert(INSERT_CONVERSION_PARAMETERS)
    int insert(ConversionParameter conversionParameter);
}
