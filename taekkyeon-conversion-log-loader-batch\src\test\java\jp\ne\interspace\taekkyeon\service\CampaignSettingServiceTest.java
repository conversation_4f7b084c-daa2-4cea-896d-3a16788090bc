/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.CampaignSettingDuplicationCutDetails;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CampaignSettingsMapper;

import static jp.ne.interspace.taekkyeon.model.DuplicationCutTarget.DAILY;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link CampaignSettingService}.
 *
 * <AUTHOR> Mayur
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class CampaignSettingServiceTest {

    @InjectMocks
    private CampaignSettingService underTest;

    @Mock
    private CampaignSettingsMapper campaignSettingsMapper;

    @Test
    public void testFindDuplicationCutDetailsByShouldReturnDuplicationCutDetailsOfCampaignCorrectlyWhenCalled() {
        // given
        long campaignId = 1;
        CampaignSettingDuplicationCutDetails duplicationCutDetails =
                new CampaignSettingDuplicationCutDetails(true, DAILY);
        when(campaignSettingsMapper.findDuplicationCutDetailsBy(campaignId))
                .thenReturn(duplicationCutDetails);

        // when
        CampaignSettingDuplicationCutDetails actual = underTest
                .findDuplicationCutDetailsBy(campaignId);

        // then
        assertNotNull(actual);
        assertTrue(actual.isEnabled());
        assertEquals(DAILY, actual.getTarget());
    }
}
