SET DATABASE SQL SYNTAX ORA TRUE;

DROP SEQUENCE IF EXISTS SALES_LOG_SEQ;
CREATE SEQUENCE  "SALES_LOG_SEQ" AS BIGINT INCREMENT BY 1 START WITH 1 ;

DROP TABLE IF EXISTS SALES_LOG_TMP;
CREATE TABLE SALES_LOG_TMP (
	"INSERT_FLAG" NUMBER(1,0) NOT NULL, 
	"SEQ_NO" NUMBER(10,0), 
	"BANNER_ID" NUMBER(10,0) NOT NULL, 
	"MERCHANT_CAMPAIGN_NO" NUMBER(10,0) NOT NULL, 
	"NODE_ID" VARCHAR2(11), 
	"CLICK_DATE" DATE, 
	"SALES_DATE" DATE NOT NULL, 
	"LOG_DATE" DATE NOT NULL, 
	"CONFIRMED_DATE" DATE, 
	"TRANSACTION_ID" VARCHAR2(256) NOT NULL, 
	"PARTNER_SITE_NO" NUMBER(10,0) NOT NULL, 
	"RANK" NUMBER(2,0), 
	"VERIFY" VARCHAR2(256), 
	"RESULT_ID" NUMBER(4,0), 
	"GOODS_ID" VARCHAR2(250), 
	"SALES_LOG_STATUS" NUMBER(2,0) NOT NULL, 
	"SALES_COUNT" NUMBER(10,0) NOT NULL, 
	"PRICE" NUMBER(10,0) NOT NULL, 
	"TOTAL_PRICE" NUMBER(10,0) NOT NULL, 
	"REWARD_TYPE" NUMBER(1,0) NOT NULL, 
	"SALES_REWARD" NUMBER(12,2) NOT NULL, 
	"TOTAL_PRICE_REWARD" NUMBER(12,2) NOT NULL, 
	"COMMISSION_TYPE" NUMBER(1,0) NOT NULL, 
	"AT_COMMISSION" NUMBER(12,2) NOT NULL, 
	"AGENT_COMMISSION" NUMBER(12,2) NOT NULL, 
	"P_AGENT_COMMISSION" NUMBER(12,2) DEFAULT 0 NOT NULL, 
	"IP" VARCHAR2(256), 
	"MEDIA_URL" VARCHAR2(512), 
	"REFERER" VARCHAR2(2048), 
	"REPEAT_COUNT" NUMBER(10,0), 
	"USER_AGENT" VARCHAR2(512), 
	"REWARD_EDIT_DATE" DATE, 
	"TRACKING_TYPE" NUMBER(2,0), 
	"DEFAULT_SALES_COUNT" NUMBER(10,0) NOT NULL, 
	"DEFAULT_PRICE" NUMBER(10,0) NOT NULL, 
	"DEFAULT_RESULT_ID" NUMBER(4,0), 
	"LP_URL" VARCHAR2(512), 
	"DEVICE_TYPE" NUMBER(2,0), 
	"POINTBACK_ID" VARCHAR2(64), 
	"PB_ID_DUPLICATIVE_FLAG" NUMBER(1,0), 
	"PB_ID_OLDEST_SALES_DATE" DATE, 
	"CREATED_BY" VARCHAR2(256), 
	"CREATED_ON" DATE, 
	"UPDATED_BY" VARCHAR2(256), 
	"UPDATED_ON" DATE, 
	"SESSION_ID" VARCHAR2(256), 
	"UUID" VARCHAR2(80) DEFAULT NULL, 
	"DEVICE_OS" NUMBER(2,0) DEFAULT 0 NOT NULL, 
	"CATEGORY_ID" VARCHAR2(250), 
	"DISCOUNT" NUMBER(12,2) DEFAULT 0, 
	"INTERNAL_TRANSACTION_ID" VARCHAR2(512), 
	"ORIGINAL_CURRENCY_TOTAL_PRICE" NUMBER(12,2), 
	"ORIGINAL_CURRENCY" VARCHAR2(3), 
	"CLICK_REFERER" VARCHAR2(2048), 
	"CLICK_URL" VARCHAR2(2048), 
	"CLICK_USER_AGENT" VARCHAR2(2048),
	"CUSTOMER_TYPE" VARCHAR2(64),
	"LANGUAGE" VARCHAR2(40),
	"CLICK_IP" VARCHAR2(256)
);

DROP TABLE IF EXISTS SALES_LOG_CV_PARAMETERS_TMP;
CREATE TABLE SALES_LOG_CV_PARAMETERS_TMP (
	"MERCHANT_CAMPAIGN_NO" NUMBER(10,0) NOT NULL, 
	"TRANSACTION_ID" VARCHAR2(256) NOT NULL, 
	"PARAM_NAME" VARCHAR2(128) NOT NULL, 
	"PARAM_VALUE" VARCHAR2(2048), 
	"CREATED_BY" VARCHAR2(256), 
	"CREATED_ON" DATE, 
	"UPDATED_BY" VARCHAR2(256), 
	"UPDATED_ON" DATE
);

DROP TABLE IF EXISTS CLICK_PARAMETERS_TMP;
CREATE TABLE CLICK_PARAMETERS_TMP (
	"MERCHANT_CAMPAIGN_NO" NUMBER(10,0) NOT NULL, 
	"INTERNAL_TRANSACTION_ID" VARCHAR2(512) NOT NULL, 
	"PARAM_NAME" VARCHAR2(128) NOT NULL, 
	"PARAM_VALUE" VARCHAR2(2048), 
	"CREATED_BY" VARCHAR2(256), 
	"CREATED_ON" DATE, 
	"UPDATED_BY" VARCHAR2(256), 
	"UPDATED_ON" DATE
);

DROP TABLE IF EXISTS MERCHANT_CAMPAIGN;
CREATE TABLE MERCHANT_CAMPAIGN (
	"CAMPAIGN_NO" NUMBER(10,0) NOT NULL, 
	"ACCOUNT_NO" NUMBER(10,0) NOT NULL, 
	"CAMPAIGN_STATE_ID" NUMBER(2,0) NOT NULL, 
	"CREATIVE_SYNC_STATUS" NUMBER(1,0), 
	"CAMPAIGN_NAME" VARCHAR2(512) NOT NULL, 
	"URL" VARCHAR2(512) NOT NULL, 
	"DESCRIPTION" VARCHAR2(4000), 
	"CATEGORY1" NUMBER(10,0) NOT NULL, 
	"CATEGORY2" NUMBER(10,0) NOT NULL, 
	"CATEGORY3" NUMBER(10,0) NOT NULL,
	"AUTO_AFF_LIMITATION_OPTION" NUMBER(1,0) NOT NULL, 
	"AUTO_AFF_LIMITATION_DIVISION" NUMBER(1,0) DEFAULT 0 NOT NULL,
	"AFF_CONDITION_SPECIAL" VARCHAR2(2048), 
	"RESULT_APPROVAL_SPECIAL" VARCHAR2(2000), 
	"PR_FOR_PARTNER" VARCHAR2(4000), 
	"GET_PARAMETER_FLAG" NUMBER(1,0) NOT NULL, 
	"POINTBACK_PERMISSION" NUMBER(1,0) NOT NULL, 
	"SELF_CONVERSION_FLAG" NUMBER(1,0), 
	"CAMPAIGN_START_DATE" DATE, 
	"CAMPAIGN_END_DATE" DATE, 
	"AUTO_AFF_APPR_DURATION" NUMBER(2,0) DEFAULT 3, 
	"OEM_FLAG" NUMBER(1,0), 
	"CREATED_BY" VARCHAR2(256), 
	"CREATED_ON" DATE, 
	"UPDATED_BY" VARCHAR2(256), 
	"UPDATED_ON" DATE, 
	"AUTO_ACTION_APPR_DURATION" NUMBER(2,0), 
	"HIDDEN_FLAG" NUMBER(1,0), 
	"START_DATE" DATE, 
	"END_DATE" DATE,
	"OVERLAP_FLG" NUMBER(1,0) DEFAULT 0, 
	"OFFER_CODE" VARCHAR2(32), 
	"DESCRIPTION_EN" VARCHAR2(4000), 
	"CAMPAIGN_TYPE" NUMBER(2,0) DEFAULT 0, 
	"IMAGE_URL" VARCHAR2(512), 
	"CURRENCY" VARCHAR2(3) DEFAULT 'IDR' NOT NULL, 
	"HIDE_CLICK_REFERRER" NUMBER(1,0) DEFAULT 1 NOT NULL,
	"REFERER_CHECK" VARCHAR2(256), 
	"DEVICE_TYPES" VARCHAR2(128)
);

DROP TABLE IF EXISTS CURRENCY_EXCHANGE_RATE_HISTORY;
CREATE TABLE CURRENCY_EXCHANGE_RATE_HISTORY (
	"CURRENCY" VARCHAR2(3) NOT NULL, 
	"TARGET_MONTH" DATE NOT NULL, 
	"RATE" NUMBER(20,10) NOT NULL, 
	"QUOTE_CURRENCY" VARCHAR2(3) NOT NULL, 
	"CREATED_BY" VARCHAR2(128), 
	"CREATED_ON" DATE, 
	"UPDATED_BY" VARCHAR2(128), 
	"UPDATED_ON" DATE
);

DROP TABLE IF EXISTS MERCHANT_CAMPAIGN_SETTING;
CREATE TABLE MERCHANT_CAMPAIGN_SETTING (
	"CAMPAIGN_NO" NUMBER(10,0), 
	"COOKIE_EXPIRATION_DATE_VIEW" NUMBER(10,0) DEFAULT 86400 NOT NULL,
	"VERIFY_CUT_FLAG" NUMBER(1,0) DEFAULT 0 NOT NULL, 
	"VERIFY_CUT_TARGET" NUMBER(1,0),
	"VERIFY_CUT_CONDITION" NUMBER(1,0),
	"CREATED_BY" VARCHAR2(256), 
	"CREATED_ON" DATE, 
	"UPDATED_BY" VARCHAR2(256), 
	"UPDATED_ON" DATE, 
	"SELF_CONVERSION_FLAG" NUMBER(1,0),
	"CV_ONLY_ONCE_FLAG" NUMBER DEFAULT 0
);

DROP TABLE IF EXISTS AFFILIATION_RANK_HISTORY;
CREATE TABLE AFFILIATION_RANK_HISTORY (
	"PARTNER_SITE_NO" NUMBER(10,0) NOT NULL, 
	"MERCHANT_CAMPAIGN_NO" NUMBER(10,0) NOT NULL, 
	"TARGET_MONTH" DATE NOT NULL, 
	"RANK" NUMBER(2,0) NOT NULL, 
	"CREATED_BY" VARCHAR2(256), 
	"CREATED_ON" DATE, 
	"UPDATED_BY" VARCHAR2(256), 
	"UPDATED_ON" DATE
);

DROP TABLE IF EXISTS COUNTRY;
CREATE TABLE COUNTRY (
    "CODE" CHAR(2 BYTE) NOT NULL,
    "NAME" VARCHAR2(256 BYTE),
    "CURRENCY" VARCHAR2(3 BYTE) NOT NULL
);

DROP TABLE IF EXISTS MERCHANT_ACCOUNT;
CREATE TABLE MERCHANT_ACCOUNT (
    "ACCOUNT_NO" NUMBER(10,0) NOT NULL,
    "MERCHANT_TYPE_ID" NUMBER(1,0) NOT NULL,
    "CORPORATE_NAME" VARCHAR2(128),
    "CORPORATE_ZIP_CODE" VARCHAR2(10),
    "CORPORATE_PREFECTURE" VARCHAR2(128),
    "CORPORATE_CITY" VARCHAR2(128),
    "CORPORATE_ADDRESS" VARCHAR2(128),
    "CORPORATE_ADDRESS2" VARCHAR2(128),
    "CORPORATE_PHONE" VARCHAR2(32),
    "CORPORATE_FAX" VARCHAR2(32),
    "CORPORATE_DIRECTOR_NAME" VARCHAR2(128),
    "CORPORATE_REMARK" VARCHAR2(2000),
    "FOSTER_LASTNAME" VARCHAR2(64),
    "FOSTER_FIRSTNAME" VARCHAR2(64),
    "FOSTER_MIDDLENAME" VARCHAR2(64),
    "FOSTER_ZIP_CODE" VARCHAR2(10),
    "FOSTER_PREFECTURE" VARCHAR2(128),
    "FOSTER_CITY" VARCHAR2(128),
    "FOSTER_ADDRESS" VARCHAR2(128),
    "FOSTER_ADDRESS2" VARCHAR2(128),
    "FOSTER_SECTION_NAME" VARCHAR2(128),
    "FOSTER_POST_NAME" VARCHAR2(128),
    "FOSTER_EMAIL" VARCHAR2(64),
    "FOSTER_PHONE" VARCHAR2(32),
    "FOSTER_FAX" VARCHAR2(32),
    "FOSTER_REMARK" VARCHAR2(2000),
    "LOGIN_NAME" VARCHAR2(64),
    "LOGIN_PASSWORD" VARCHAR2(32),
    "ACCOUNT_STATE" NUMBER(1,0) NOT NULL,
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE,
    "ACCOUNTANT_LASTNAME" VARCHAR2(64),
    "ACCOUNTANT_FIRSTNAME" VARCHAR2(64),
    "ACCOUNTANT_MIDDLENAME" VARCHAR2(64),
    "ACCOUNTANT_EMAIL" VARCHAR2(64),
    "ACCOUNTANT_PHONE" VARCHAR2(32),
    "U_ID" VARCHAR2(32) DEFAULT 'v5542527tvx7w4ts6suvswssss2137xx',
    "COUNTRY_CODE" VARCHAR2(2) DEFAULT 'ID' NOT NULL
);

DROP TABLE IF EXISTS RESULT_TARGET_SETTING;
CREATE TABLE RESULT_TARGET_SETTING (
	"MERCHANT_CAMPAIGN_NO" NUMBER(10,0) NOT NULL, 
	"RESULT_ID" NUMBER(4,0) NOT NULL, 
	"RESULT_NAME" VARCHAR2(128 BYTE) NOT NULL, 
	"REWARD_TYPE" NUMBER(1,0) NOT NULL,
	"USE_FLAG" NUMBER(1,0) NOT NULL, 
	"PARTNER_INVISIBLE_FLAG" NUMBER(1,0) NOT NULL,
	"CREATED_BY" VARCHAR2(256), 
	"CREATED_ON" DATE, 
	"UPDATED_BY" VARCHAR2(256), 
	"UPDATED_ON" DATE, 
	"AUTO_ACTION_APPR_DURATION" NUMBER(3,0)
);

DROP TABLE IF EXISTS PARTNER_ACCOUNT;
CREATE TABLE PARTNER_ACCOUNT (
	"ACCOUNT_NO" NUMBER(10,0) NOT NULL,
	"ACCOUNT_TYPE_ID" NUMBER(1,0) DEFAULT 0,
	"PARTNER_TYPE_ID" NUMBER(2,0),
	"CORPORATE_NAME" VARCHAR2(128),
	"SECTION_NAME" VARCHAR2(128),
	"POST_NAME" VARCHAR2(128),
	"LASTNAME" VARCHAR2(64),
	"FIRSTNAME" VARCHAR2(64),
	"EMAIL" VARCHAR2(64),
	"ZIP_CODE" VARCHAR2(8),
	"PREFECTURE" VARCHAR2(128),
	"CITY" VARCHAR2(128),
	"ADDRESS" VARCHAR2(512),
	"ADDRESS2" VARCHAR2(512),
	"PHONE" VARCHAR2(32),
	"BIRTHDAY" DATE,
	"SEX" NUMBER(1,0),
	"COMMERCIAL_REGISTRATION_NUMBER" VARCHAR2(128),
	"VAT_NUMBER" VARCHAR2(128),
	"BANK_ID" VARCHAR2(5),
	"BANK_NAME" VARCHAR2(128),
	"BANK_BRANCH_ID" VARCHAR2(8),
	"BANK_BRANCH_NAME" VARCHAR2(128),
	"BANK_ACCOUNT_TYPE_ID" NUMBER(2,0),
	"BANK_ACCOUNT_NUMBER" VARCHAR2(30),
	"BANK_ACCOUNT_OWNER_LASTNAME" VARCHAR2(128),
	"BANK_ACCOUNT_OWNER_FIRSTNAME" VARCHAR2(128),
	"URL" VARCHAR2(256),
	"LOGIN_NAME" VARCHAR2(64),
	"LOGIN_PASSWORD" VARCHAR2(32),
	"ACCOUNT_STATE" NUMBER(1,0) NOT NULL,
	"APPLIED_DATE" DATE NOT NULL,
	"QUIT_DATE" DATE,
	"ORIGIN_NO" NUMBER(10,0),
	"CREATED_BY" VARCHAR2(256),
	"CREATED_ON" DATE,
	"UPDATED_BY" VARCHAR2(256),
	"UPDATED_ON" DATE,
	"BLACKLIST_FLAG" NUMBER(1,0),
	"U_ID" VARCHAR2(32) DEFAULT 'v5542527tvx7w4ts6suvswssss2137xx',
	"AGENCY_ID" NUMBER(10,0) DEFAULT 0,
	"COUNTRY_CODE" VARCHAR2(2) DEFAULT 'ID' NOT NULL
);

DROP TABLE IF EXISTS PUBLISHER_AGENCY;
CREATE TABLE PUBLISHER_AGENCY (
	"ID" NUMBER(10,0),
	"NAME" VARCHAR2(128),
	"COMMISSION_POLICY" NUMBER(2,0) DEFAULT 0,
	"CREATED_BY" VARCHAR2(256),
	"CREATED_ON" DATE,
	"UPDATED_BY" VARCHAR2(256),
	"UPDATED_ON" DATE
);

DROP TABLE IF EXISTS PARTNER_SITE;
CREATE TABLE PARTNER_SITE (	
	"SITE_NO" NUMBER(10,0), 
	"ACCOUNT_NO" NUMBER(10,0), 
	"SITE_NAME" VARCHAR2(1024) DEFAULT 0, 
	"URL" VARCHAR2(1024) DEFAULT 0, 
	"DESCRIPTION" VARCHAR2(2000) DEFAULT 0, 
	"SITE_TYPE" NUMBER(2,0) DEFAULT 0, 
	"SITE_STATE" NUMBER(1,0) DEFAULT 0, 
	"CATEGORY_LOW_ID1" NUMBER(10,0), 
	"CATEGORY_LOW_ID2" NUMBER(10,0), 
	"CATEGORY_LOW_ID3" NUMBER(10,0),
	"MAIN_SITE_FLAG" NUMBER(1,0),
	"POINTBACK_FLAG" NUMBER(1,0), 
	"ALL_BANNERS_FLG" NUMBER(1,0) DEFAULT 0, 
	"CREATED_BY" VARCHAR2(256), 
	"CREATED_ON" DATE, 
	"UPDATED_BY" VARCHAR2(256), 
	"UPDATED_ON" DATE
);

DROP TABLE IF EXISTS SALES_LOG;
CREATE TABLE SALES_LOG (
	"SEQ_NO" NUMBER(10,0), 
	"BANNER_ID" NUMBER(10,0), 
	"MERCHANT_CAMPAIGN_NO" NUMBER(10,0),
	"CLICK_DATE" DATE, 
	"SALES_DATE" DATE, 
	"LOG_DATE" DATE, 
	"CONFIRMED_DATE" DATE, 
	"TRANSACTION_ID" VARCHAR2(256), 
	"PARTNER_SITE_NO" NUMBER(10,0), 
	"RANK" NUMBER(2,0), 
	"VERIFY" VARCHAR2(256), 
	"RESULT_ID" NUMBER(4,0), 
	"GOODS_ID" VARCHAR2(250), 
	"SALES_LOG_STATUS" NUMBER(2,0), 
	"SALES_COUNT" NUMBER(10,0), 
	"PRICE" NUMBER(10,0), 
	"TOTAL_PRICE" NUMBER(10,0), 
	"REWARD_TYPE" NUMBER(1,0), 
	"SALES_REWARD" NUMBER(12,2), 
	"TOTAL_PRICE_REWARD" NUMBER(12,2), 
	"COMMISSION_TYPE" NUMBER(1,0), 
	"AT_COMMISSION" NUMBER(12,2), 
	"AGENT_COMMISSION" NUMBER(12,2), 
	"P_AGENT_COMMISSION" NUMBER(12,2) DEFAULT 0, 
	"IP" VARCHAR2(256), 
	"MEDIA_URL" VARCHAR2(512), 
	"REFERER" VARCHAR2(2048), 
	"REPEAT_COUNT" NUMBER(10,0), 
	"USER_AGENT" VARCHAR2(512), 
	"REWARD_EDIT_DATE" DATE,
	"TRACKING_TYPE" NUMBER(2,0), 
	"DEFAULT_SALES_COUNT" NUMBER(10,0), 
	"DEFAULT_PRICE" NUMBER(10,0), 
	"DEFAULT_RESULT_ID" NUMBER(4,0), 
	"LP_URL" VARCHAR2(512), 
	"DEVICE_TYPE" NUMBER(2,0), 
	"POINTBACK_ID" VARCHAR2(64), 
	"PB_ID_DUPLICATIVE_FLAG" NUMBER(1,0), 
	"PB_ID_OLDEST_SALES_DATE" DATE, 
	"CREATED_BY" VARCHAR2(256), 
	"CREATED_ON" DATE, 
	"UPDATED_BY" VARCHAR2(256), 
	"UPDATED_ON" DATE, 
	"NEW_FLAG" NUMBER(1,0) DEFAULT 0, 
	"SESSION_ID" VARCHAR2(256), 
	"CURRENCY_ID" NUMBER(4,0), 
	"ORIGIN_PRICE" NUMBER(12,2), 
	"ORIGIN_TOTAL_PRICE" NUMBER(12,2), 
	"UUID" VARCHAR2(80) DEFAULT NULL, 
	"DEVICE_OS" NUMBER(2,0) DEFAULT 0, 
	"CATEGORY_ID" VARCHAR2(250), 
	"DISCOUNT" NUMBER(12,2) DEFAULT 0, 
	"INTERNAL_TRANSACTION_ID" VARCHAR2(512), 
	"ORIGINAL_CURRENCY_TOTAL_PRICE" NUMBER(12,2), 
	"ORIGINAL_CURRENCY" VARCHAR2(3), 
	"CLICK_REFERER" VARCHAR2(2048), 
	"CLICK_URL" VARCHAR2(2048), 
	"CLICK_USER_AGENT" VARCHAR2(2048),
	"POSTBACK_STATUS" NUMBER(1,0) DEFAULT 1,
	"CUSTOMER_TYPE" VARCHAR2(64),
	"AT_COMMISSION_IN_USD" NUMBER(12,2),
	"PUBLISHER_REWARD_IN_USD" NUMBER(12,2),
	"PUBLISHER_AGENT_COMMISSION_IN_USD" NUMBER(12,2),
	"MERCHANT_AGENT_COMMISSION_IN_USD" NUMBER(12,2),
	"TRANSACTION_AMOUNT_IN_USD" NUMBER(12,2),
	"DISCOUNT_AMOUNT_IN_USD" NUMBER(12,2),
	"UNIT_PRICE_IN_USD" NUMBER(12,2),
	"LANGUAGE" VARCHAR2(40),
	"CLICK_IP" VARCHAR2(256)
);

DROP TABLE IF EXISTS SALES_LOG_CV_PARAMETERS;
CREATE TABLE SALES_LOG_CV_PARAMETERS (
	"MERCHANT_CAMPAIGN_NO" NUMBER(10,0), 
	"TRANSACTION_ID" VARCHAR2(256), 
	"PARAM_NAME" VARCHAR2(128), 
	"PARAM_VALUE" VARCHAR2(2048), 
	"CREATED_BY" VARCHAR2(256), 
	"CREATED_ON" DATE, 
	"UPDATED_BY" VARCHAR2(256), 
	"UPDATED_ON" DATE
);

DROP TABLE IF EXISTS CLICK_PARAMETERS;
CREATE TABLE CLICK_PARAMETERS (
	"MERCHANT_CAMPAIGN_NO" NUMBER(10,0) NOT NULL, 
	"INTERNAL_TRANSACTION_ID" VARCHAR2(512) NOT NULL, 
	"PARAM_NAME" VARCHAR2(128) NOT NULL, 
	"PARAM_VALUE" VARCHAR2(2048), 
	"CREATED_BY" VARCHAR2(256), 
	"CREATED_ON" DATE, 
	"UPDATED_BY" VARCHAR2(256), 
	"UPDATED_ON" DATE
);

DROP TABLE IF EXISTS POSTBACK_URL;
CREATE TABLE POSTBACK_URL (
    "PARTNER_SITE_NO" NUMBER(10,0) NOT NULL, 
    "BANNER_ID" NUMBER(10,0) NOT NULL, 
    "POSTBACK_URL" VARCHAR2(1024 BYTE), 
    "APPROVE_FLAG" NUMBER(1,0), 
    "CREATED_BY" VARCHAR2(256 BYTE), 
    "CREATED_ON" DATE, 
    "UPDATED_BY" VARCHAR2(256 BYTE), 
    "UPDATED_ON" DATE
);