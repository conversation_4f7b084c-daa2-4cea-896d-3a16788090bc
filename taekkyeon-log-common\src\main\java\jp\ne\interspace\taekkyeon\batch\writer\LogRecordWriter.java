/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.writer;

import java.util.LinkedList;
import java.util.List;

import com.google.inject.Inject;

import lombok.extern.slf4j.Slf4j;

import org.easybatch.core.record.Batch;
import org.easybatch.core.record.Record;
import org.easybatch.core.writer.RecordWriter;

import jp.ne.interspace.taekkyeon.loader.LogItemLoader;
import jp.ne.interspace.taekkyeon.model.LogItem;
import jp.ne.interspace.taekkyeon.model.LogRecordPayload;
import jp.ne.interspace.taekkyeon.persist.aws.sqs.LogQueueByDefault;

/**
 * {@link RecordWriter} implementation for adding log data to Oracle.
 *
 * <AUTHOR>
 */
@Slf4j
public class LogRecordWriter implements RecordWriter {

    @Inject
    private LogItemLoader logItemLoader;

    @Inject
    private LogQueueByDefault logQueueByDefault;

    @Override
    public void open() throws Exception {
        // do nothing
    }

    @Override
    public void writeRecords(Batch batch) throws Exception {
        loadLogItems(batch);
    }

    @Override
    public void close() throws Exception {
        // do nothing
    }

    @SuppressWarnings("unchecked")
    private void loadLogItems(Batch records) {
        List<String> receiptHandles = new LinkedList<>();
        for (Record<LogRecordPayload> record : records) {
            LogRecordPayload payload = record.getPayload();

            LogItem logItem = payload.getLogItem();
            if (logItem != null) {
                try {
                    logItemLoader.load(logItem);
                    receiptHandles.add(payload.getReceiptHandle());
                } catch (Exception ex) {
                    log.error("Failed to load log: " + logItem, ex);
                }
            } else {
                receiptHandles.add(payload.getReceiptHandle());
            }
        }
        logQueueByDefault.delete(receiptHandles);
    }
}
