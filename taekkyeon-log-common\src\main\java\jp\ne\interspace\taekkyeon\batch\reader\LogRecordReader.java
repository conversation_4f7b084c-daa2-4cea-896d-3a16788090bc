/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.reader;

import com.google.inject.Inject;

import org.easybatch.core.reader.RecordReader;

import jp.ne.interspace.taekkyeon.persist.aws.sqs.LogQueueByDefault;
import jp.ne.interspace.taekkyeon.persist.aws.sqs.SimpleQueueServiceQueue;

/**
 * {@link RecordReader} implementation for loading various event log items.
 *
 * <AUTHOR> Varga
 */
public class LogRecordReader extends SqsRecordReader {

    @Inject
    private LogQueueByDefault logQueueByDefault;

    @Override
    protected SimpleQueueServiceQueue getSourceQueue() {
        return logQueueByDefault;
    }
}
