/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.factory;

import jp.ne.interspace.taekkyeon.model.LogItem;

/**
 * Base interface for classes that create various {@link LogItem}s from raw log items.
 *
 * <AUTHOR>
 */
public interface LogItemFactory {

    /**
     * Creates a {@link LogItem} from {@code rawLogItem}.
     *
     * @param rawLogItem
     *            the raw item to parse
     * @return the created {@link LogItem}.
     */
    LogItem createFrom(String rawLogItem);
}
