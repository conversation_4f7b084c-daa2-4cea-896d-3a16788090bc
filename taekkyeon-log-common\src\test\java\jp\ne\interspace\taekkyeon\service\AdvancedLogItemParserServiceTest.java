/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.Map;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.util.DateUtils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link AdvancedLogItemParserService}.
 *
 * <AUTHOR> Tran
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class AdvancedLogItemParserServiceTest {

    private static final String REQUEST_TIME = "REQUEST_TIME";
    private static final String MERCHANT_CAMPAIGN_NO = "MERCHANT_CAMPAIGN_NO";
    private static final String CLICK_DATE = "CLICK_DATE";
    private static final String CONVERSION_DATE = "SALES_DATE";

    private static final String VALID_RAW_ID = "1";
    private static final long VALID_ID = 1;

    private static final LocalDateTime PARSED_LOG_DATE = LocalDateTime.of(2017, 12, 6, 18, 3, 31);
    private static final LocalDateTime EXPECTED_LOCAL_DATE_TIME = LocalDateTime.of(2017, 12, 6, 19, 3, 31);

    @Spy @InjectMocks
    private AdvancedLogItemParserService underTest;

    @Mock
    private LogItemParserService logItemParserService;

    @Mock
    private MerchantAccountService merchantAccountService;

    @Mock
    private CountryService countryService;

    @Mock
    private DateUtils dateUtils;

    @Test
    public void testGetLogDateFromShouldReturnCorrectLogDateWhenLogDateHasCorrectFormat() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(REQUEST_TIME, "2017-12-06 18:03:31");
        parsedLogItem.put(MERCHANT_CAMPAIGN_NO, VALID_RAW_ID);

        doReturn(VALID_ID).when(logItemParserService).getCampaignIdFrom(parsedLogItem);
        doReturn(EXPECTED_LOCAL_DATE_TIME).when(underTest).createDateFrom(VALID_ID,
                PARSED_LOG_DATE);

        // when
        LocalDateTime actual = underTest.getLogDateFrom(parsedLogItem);

        // then
        assertEquals(EXPECTED_LOCAL_DATE_TIME, actual);
    }

    @Test(expected = DateTimeParseException.class)
    public void testGetLogDateFromShouldThrowExceptionWhenLogDateHasIncorrectFormat() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(REQUEST_TIME, "dateWithIncorrectFormat");
        parsedLogItem.put(MERCHANT_CAMPAIGN_NO, VALID_RAW_ID);

        // when
        underTest.getLogDateFrom(parsedLogItem);
    }

    @Test
    public void testGetClickDateFromShouldReturnCorrectClickDateWhenClickDateHasCorrectFormat() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(CLICK_DATE, "2017-12-06 18:03:31");
        parsedLogItem.put(MERCHANT_CAMPAIGN_NO, VALID_RAW_ID);

        doReturn(VALID_ID).when(logItemParserService).getCampaignIdFrom(parsedLogItem);
        doReturn(EXPECTED_LOCAL_DATE_TIME).when(underTest).createDateFrom(VALID_ID,
                PARSED_LOG_DATE);

        // when
        LocalDateTime actual = underTest.getClickDateFrom(parsedLogItem);

        // then
        assertEquals(EXPECTED_LOCAL_DATE_TIME, actual);
    }

    @Test
    public void testGetClickDateFromShouldThrowExceptionWhenGivenEmptyData() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();

        // when
        try {
            underTest.getClickDateFrom(parsedLogItem);
            fail();
        } catch (TaekkyeonException ex) {

            // then
            assertEquals("The parameter [CLICK_DATE] is invalid", ex.getMessage());
        }
    }

    @Test
    public void testGetConversionDateFromShouldReturnCorrectConversionDateWhenGivenCorrectFormat() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(CONVERSION_DATE, "2017-12-06 18:03:31");
        parsedLogItem.put(MERCHANT_CAMPAIGN_NO, VALID_RAW_ID);

        // when
        LocalDateTime actual = underTest.getConversionDateFrom(parsedLogItem);

        // then
        assertEquals(PARSED_LOG_DATE, actual);
        verify(underTest, never()).getLogDateFrom(anyMap());
    }

    @Test
    public void testGetConversionDateFromShouldReturnCorrectConversionDateWhenGivenCorrectIso8061Format() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(CONVERSION_DATE, "2017-12-06T18:03:31");
        parsedLogItem.put(MERCHANT_CAMPAIGN_NO, VALID_RAW_ID);

        doReturn(VALID_ID).when(logItemParserService).getCampaignIdFrom(parsedLogItem);

        // when
        LocalDateTime actual = underTest.getConversionDateFrom(parsedLogItem);

        // then
        assertEquals(PARSED_LOG_DATE, actual);
        verify(underTest, never()).getLogDateFrom(anyMap());
    }

    @Test
    public void testGetConversionDateFromShouldReturnCorrectConversionDateWhenGivenCorrectYyyySlashMmSlashDdHhMmSsDateFormat() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(CONVERSION_DATE, "2017/12/06 18:03:31");
        parsedLogItem.put(MERCHANT_CAMPAIGN_NO, VALID_RAW_ID);

        doReturn(VALID_ID).when(logItemParserService).getCampaignIdFrom(parsedLogItem);

        // when
        LocalDateTime actual = underTest.getConversionDateFrom(parsedLogItem);

        // then
        assertEquals(PARSED_LOG_DATE, actual);
        verify(underTest, never()).getLogDateFrom(anyMap());
    }

    @Test
    public void testGetConversionDateFromShouldReturnCorrectConversionDateWhenGivenCorrectYyyySlashMmSlashDdTHhMmSsDateFormat() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(CONVERSION_DATE, "2017/12/06T18:03:31");
        parsedLogItem.put(MERCHANT_CAMPAIGN_NO, VALID_RAW_ID);

        doReturn(VALID_ID).when(logItemParserService).getCampaignIdFrom(parsedLogItem);

        // when
        LocalDateTime actual = underTest.getConversionDateFrom(parsedLogItem);

        // then
        assertEquals(PARSED_LOG_DATE, actual);
        verify(underTest, never()).getLogDateFrom(anyMap());
    }

    @Test
    public void testGetConversionDateFromShouldReturnCorrectLogDateWhenNonExistentConversionDate() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(REQUEST_TIME, "2017-12-06 19:03:31");
        parsedLogItem.put(MERCHANT_CAMPAIGN_NO, VALID_RAW_ID);

        doReturn(EXPECTED_LOCAL_DATE_TIME).when(underTest).getLogDateFrom(parsedLogItem);

        // when
        LocalDateTime actual = underTest.getConversionDateFrom(parsedLogItem);

        // then
        assertEquals(EXPECTED_LOCAL_DATE_TIME, actual);
    }

    @Test
    public void testGetConversionDateFromShouldReturnCorrectLogDateWhenConversionDateIsNull() {
        // given
        Map<String, String> parsedLogItem = new HashMap<>();
        parsedLogItem.put(CONVERSION_DATE, null);
        parsedLogItem.put(REQUEST_TIME, "2017-12-06 19:03:31");
        parsedLogItem.put(MERCHANT_CAMPAIGN_NO, VALID_RAW_ID);

        doReturn(EXPECTED_LOCAL_DATE_TIME).when(underTest).getLogDateFrom(parsedLogItem);

        // when
        LocalDateTime actual = underTest.getConversionDateFrom(parsedLogItem);

        // then
        assertEquals(EXPECTED_LOCAL_DATE_TIME, actual);
    }

    @Test
    public void testCreateDateFromShouldReturnCorrectDateWhenCalled() {
        // given
        String countryCode = "ID";
        when(merchantAccountService.findCountryCodeBy(VALID_ID)).thenReturn(countryCode);

        String zoneId = "Asia/Jakarta";
        when(countryService.findZoneIdBy(countryCode)).thenReturn(zoneId);

        ZonedDateTime convertedByTimezone = ZonedDateTime.of(2017, 12, 6, 19, 3, 31, 0,
                ZoneId.of(zoneId));
        when(dateUtils.convertByTimeZone(PARSED_LOG_DATE, zoneId))
                .thenReturn(convertedByTimezone);

        // when
        LocalDateTime actual = underTest.createDateFrom(VALID_ID, PARSED_LOG_DATE);

        // then
        assertEquals(EXPECTED_LOCAL_DATE_TIME, actual);
    }

}
