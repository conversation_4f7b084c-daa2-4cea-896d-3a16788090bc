/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.writer;

import java.util.Arrays;
import java.util.List;

import org.easybatch.core.record.Batch;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.loader.LogItemLoader;
import jp.ne.interspace.taekkyeon.model.LogItem;
import jp.ne.interspace.taekkyeon.model.LogRecord;
import jp.ne.interspace.taekkyeon.model.LogRecordPayload;
import jp.ne.interspace.taekkyeon.persist.aws.sqs.LogQueueByDefault;

import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

/**
 * Unit test for {@link LogRecordWriter}.
 *
 * <AUTHOR> Varga
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class LogRecordWriterTest {

    private static final String RECEIPT_HANDLE_1 = "receiptHandle1";
    private static final String RECEIPT_HANDLE_2 = "receiptHandle2";
    private static final String RECEIPT_HANDLE_3 = "receiptHandle3";

    @InjectMocks @Spy
    private LogRecordWriter underTest;

    @Mock
    private LogItemLoader logItemLoader;

    @Mock
    private LogQueueByDefault logQueueByDefault;

    @Test
    public void testWriteRecordsShouldNotThrowExceptionWhenTaekkyeonExceptionOccurs()
            throws Exception {
        // given
        LogItem item1 = mock(LogItem.class);
        LogItem item2 = mock(LogItem.class);

        Batch batch = new Batch(
                new LogRecord(null, new LogRecordPayload(RECEIPT_HANDLE_1, item1)),
                new LogRecord(null, new LogRecordPayload(RECEIPT_HANDLE_2, item2)),
                new LogRecord(null, new LogRecordPayload(RECEIPT_HANDLE_3, null)));

        List<String> receiptHandles = Arrays.asList(RECEIPT_HANDLE_2, RECEIPT_HANDLE_3);

        doThrow(new TaekkyeonException("DELIBERATELY_THROWN_EXCEPTION"))
                .when(logItemLoader).load(item1);

        // when
        underTest.writeRecords(batch);

        // then
        verify(logItemLoader).load(item1);
        verify(logItemLoader).load(item2);
        verify(logQueueByDefault).delete(receiptHandles);
    }
}
