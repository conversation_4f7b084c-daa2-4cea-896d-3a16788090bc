/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * Mybatis mapper for handling data of postback url.
 *
 * <AUTHOR> Shin
 */
public interface PostbackUrlMapper {

    /**
        SELECT
            COUNT(p.banner_id)
        FROM
            postback_url p
        INNER JOIN
            partner_site ps
        ON
            p.partner_site_no = ps.site_no
        WHERE
            p.partner_site_no = #{siteId}
        AND (
              p.banner_id = #{creativeId}
            OR (
                p.banner_id = 0
              AND
                ps.all_banners_flg = 1
            )
        )
     */
    @Multiline String SELECT_SITE_ID_WITH_POSTBACK = "";

    /**
     * Returns {@code true}, if the given site has postback set up, or {@code null}
     * otherwise.
     *
     * @param siteId
     *          ID of the given site
     * @param creativeId
     *          ID of the given creative
     * @return {@code true}, if the given site has postback set up, or {@code null}
     *         otherwise
     */
    @Select(SELECT_SITE_ID_WITH_POSTBACK)
    boolean isPostbackAvailableFor(@Param("siteId") long siteId,
            @Param("creativeId") long creativeId);
}
