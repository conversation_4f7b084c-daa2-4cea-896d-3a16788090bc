SET DATABASE SQL SYNTAX ORA TRUE;

INSERT INTO AFFILIATION_RANK_HISTORY (PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, TARGET_MONTH, RANK)
VALUES (1, 1, TO_DATE('201701', '<PERSON><PERSON><PERSON><PERSON><PERSON>'), 5);
INSERT INTO AFFILIATION_RANK_HISTORY (PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, TARGET_MONTH, RANK)
VALUES (1, 2, TO_DATE('201701', 'YYYYMM'), 6);
INSERT INTO AFFILIATION_RANK_HISTORY (PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, TARGET_MONTH, RANK)
VALUES (1, 4, TO_DATE('201701', 'YYYYMM'), 5);

INSERT INTO MERCHANT_CAMPAIGN_SETTING (CAMPAIGN_NO, VERIFY_CUT_FLAG, VERIFY_CUT_TARGET, VERIFY_CUT_CONDITION)
VALUES (1, 1, 1, 0);
INSERT INTO MERCHANT_CAMPAIGN_SETTING (CAMPAIGN_NO, VERIFY_CUT_FLAG, VERIFY_CUT_TARGET, VERIFY_CUT_CONDITION)
VALUES (2, 1, 0, 0);
INSERT INTO MERCHANT_CAMPAIGN_SETTING (CAMPAIGN_NO, VERIFY_CUT_FLAG)
VALUES (3, 0);
INSERT INTO MERCHANT_CAMPAIGN_SETTING (CAMPAIGN_NO, VERIFY_CUT_FLAG)
VALUES (4, 1);

INSERT INTO MERCHANT_CAMPAIGN (CAMPAIGN_NO, ACCOUNT_NO, CAMPAIGN_STATE_ID, CAMPAIGN_NAME, URL, IMAGE_URL, DESCRIPTION, CATEGORY1, CATEGORY2, CATEGORY3, AUTO_AFF_LIMITATION_OPTION, AUTO_AFF_LIMITATION_DIVISION, GET_PARAMETER_FLAG, SELF_CONVERSION_FLAG, POINTBACK_PERMISSION, CAMPAIGN_START_DATE, CAMPAIGN_END_DATE, CAMPAIGN_TYPE, CURRENCY, REFERER_CHECK)
VALUES(1, 1, 1, 'campaign1', 'http://test1.ne.jp', 'http://test11.ne.jp', 'This is test', 0, 0, 0, 0, 0, 0, 1, 0, TO_DATE('2016/03/05', 'YYYY/MM/DD'), TO_DATE('2016/07/03', 'YYYY/MM/DD'), 0, 'USD', 'google.co.jp,interspace.co.jp');
INSERT INTO MERCHANT_CAMPAIGN (CAMPAIGN_NO, ACCOUNT_NO, CAMPAIGN_STATE_ID, CAMPAIGN_NAME, URL, IMAGE_URL, DESCRIPTION, CATEGORY1, CATEGORY2, CATEGORY3, AUTO_AFF_LIMITATION_OPTION, AUTO_AFF_LIMITATION_DIVISION, GET_PARAMETER_FLAG, SELF_CONVERSION_FLAG, POINTBACK_PERMISSION, CAMPAIGN_START_DATE, CAMPAIGN_END_DATE, CAMPAIGN_TYPE, CURRENCY, REFERER_CHECK)
VALUES(2, 1, 1, 'campaign2', 'http://test2.ne.jp', 'http://test22.ne.jp', 'This is test', 0, 0, 0, 0, 0, 0, 1, 0, TO_DATE('2016/03/05', 'YYYY/MM/DD'), TO_DATE('2016/07/03', 'YYYY/MM/DD'), 0, 'IDR', 'google.co.jp,interspace.co.jp');
INSERT INTO MERCHANT_CAMPAIGN (CAMPAIGN_NO, ACCOUNT_NO, CAMPAIGN_STATE_ID, CAMPAIGN_NAME, URL, IMAGE_URL, DESCRIPTION, CATEGORY1, CATEGORY2, CATEGORY3, AUTO_AFF_LIMITATION_OPTION, AUTO_AFF_LIMITATION_DIVISION, GET_PARAMETER_FLAG, SELF_CONVERSION_FLAG, POINTBACK_PERMISSION, CAMPAIGN_START_DATE, CAMPAIGN_END_DATE, CAMPAIGN_TYPE, CURRENCY, REFERER_CHECK)
VALUES(3, 1, 1, 'campaign3', 'http://test3.ne.jp', 'http://test33.ne.jp', 'This is test', 0, 0, 0, 0, 0, 0, 1, 0, TO_DATE('2016/03/05', 'YYYY/MM/DD'), TO_DATE('2016/07/03', 'YYYY/MM/DD'), 0, 'USD', 'google.co.jp,interspace.co.jp');
INSERT INTO MERCHANT_CAMPAIGN (CAMPAIGN_NO, ACCOUNT_NO, CAMPAIGN_STATE_ID, CAMPAIGN_NAME, URL, IMAGE_URL, DESCRIPTION, CATEGORY1, CATEGORY2, CATEGORY3, AUTO_AFF_LIMITATION_OPTION, AUTO_AFF_LIMITATION_DIVISION, GET_PARAMETER_FLAG, SELF_CONVERSION_FLAG, POINTBACK_PERMISSION, CAMPAIGN_START_DATE, CAMPAIGN_END_DATE, CAMPAIGN_TYPE, CURRENCY, REFERER_CHECK)
VALUES(4, 1, 1, 'campaign4', 'http://test4.ne.jp', 'http://test44.ne.jp', 'This is test', 0, 0, 0, 0, 0, 0, 1, 0, TO_DATE('2016/03/05', 'YYYY/MM/DD'), TO_DATE('2016/07/03', 'YYYY/MM/DD'), 0, 'USD', 'google.co.jp,interspace.co.jp');

INSERT INTO CURRENCY_EXCHANGE_RATE_HISTORY (CURRENCY, TARGET_MONTH, RATE, QUOTE_CURRENCY)
VALUES ('USD', TO_DATE('201701', 'YYYYMM'), 1.2, 'MYR');
INSERT INTO CURRENCY_EXCHANGE_RATE_HISTORY (CURRENCY, TARGET_MONTH, RATE, QUOTE_CURRENCY)
VALUES ('JPY', TO_DATE('201701', 'YYYYMM'), 1.5, 'MYR');

INSERT INTO COUNTRY (CODE, CURRENCY, ZONE_ID)
VALUES ('ID', 'IDR', 'Asia/Jakarta');
INSERT INTO COUNTRY (CODE, CURRENCY, ZONE_ID)
VALUES ('MY', 'MYR', 'Asia/Kuala_Lumpur');
INSERT INTO COUNTRY (CODE, CURRENCY, ZONE_ID)
VALUES ('SG', 'SGD', 'Asia/Singapore');
INSERT INTO COUNTRY (CODE, CURRENCY, ZONE_ID)
VALUES ('TH', 'THB', 'Asia/Bangkok');

INSERT INTO MERCHANT_ACCOUNT (ACCOUNT_NO, MERCHANT_TYPE_ID, ACCOUNT_STATE, COUNTRY_CODE)
VALUES (1, 1, 1, 'MY');

INSERT INTO RESULT_TARGET_SETTING (MERCHANT_CAMPAIGN_NO, RESULT_ID, RESULT_NAME, REWARD_TYPE, USE_FLAG, PARTNER_INVISIBLE_FLAG)
VALUES (1, 1, 'TEST1', 1, 1, 0);
INSERT INTO RESULT_TARGET_SETTING (MERCHANT_CAMPAIGN_NO, RESULT_ID, RESULT_NAME, REWARD_TYPE, USE_FLAG, PARTNER_INVISIBLE_FLAG)
VALUES (1, 2, 'TEST2', 1, 1, 0);
INSERT INTO RESULT_TARGET_SETTING (MERCHANT_CAMPAIGN_NO, RESULT_ID, RESULT_NAME, REWARD_TYPE, USE_FLAG, PARTNER_INVISIBLE_FLAG)
VALUES (1, 3, 'TEST3', 1, 1, 0);
INSERT INTO RESULT_TARGET_SETTING (MERCHANT_CAMPAIGN_NO, RESULT_ID, RESULT_NAME, REWARD_TYPE, USE_FLAG, PARTNER_INVISIBLE_FLAG)
VALUES (1, 30, 'TEST30', 1, 1, 0);
INSERT INTO RESULT_TARGET_SETTING (MERCHANT_CAMPAIGN_NO, RESULT_ID, RESULT_NAME, REWARD_TYPE, USE_FLAG, PARTNER_INVISIBLE_FLAG)
VALUES (3, 1, 'TEST31', 1, 1, 0);
INSERT INTO RESULT_TARGET_SETTING (MERCHANT_CAMPAIGN_NO, RESULT_ID, RESULT_NAME, REWARD_TYPE, USE_FLAG, PARTNER_INVISIBLE_FLAG)
VALUES (3, 3, 'TEST33', 1, 1, 0);
INSERT INTO RESULT_TARGET_SETTING (MERCHANT_CAMPAIGN_NO, RESULT_ID, RESULT_NAME, REWARD_TYPE, USE_FLAG, PARTNER_INVISIBLE_FLAG)
VALUES (10, 1, 'TEST10', 1, 1, 0);
INSERT INTO RESULT_TARGET_SETTING (MERCHANT_CAMPAIGN_NO, RESULT_ID, RESULT_NAME, REWARD_TYPE, USE_FLAG, PARTNER_INVISIBLE_FLAG)
VALUES (11, 2, 'TEST11', 1, 1, 0);
INSERT INTO RESULT_TARGET_SETTING (MERCHANT_CAMPAIGN_NO, RESULT_ID, RESULT_NAME, REWARD_TYPE, USE_FLAG, PARTNER_INVISIBLE_FLAG)
VALUES (4, 2, 'TEST4', 1, 1, 0);

INSERT INTO PARTNER_ACCOUNT (ACCOUNT_NO, AGENCY_ID, ACCOUNT_STATE, APPLIED_DATE, COUNTRY_CODE) VALUES (9, 0, 1, TO_DATE('2018/02/06', 'YYYY/MM/DD'), 'ID');
INSERT INTO PARTNER_ACCOUNT (ACCOUNT_NO, AGENCY_ID, ACCOUNT_STATE, APPLIED_DATE, COUNTRY_CODE) VALUES (10, 1, 1, TO_DATE('2018/02/06', 'YYYY/MM/DD'), 'MY');
INSERT INTO PARTNER_ACCOUNT (ACCOUNT_NO, AGENCY_ID, ACCOUNT_STATE, APPLIED_DATE, COUNTRY_CODE) VALUES (11, 2, 1, TO_DATE('2018/02/06', 'YYYY/MM/DD'), 'SG');

INSERT INTO PUBLISHER_AGENCY (ID, COMMISSION_POLICY) VALUES (0, 1);
INSERT INTO PUBLISHER_AGENCY (ID, COMMISSION_POLICY) VALUES (2, 2);

INSERT INTO PARTNER_SITE (SITE_NO,ACCOUNT_NO,SITE_NAME,URL,DESCRIPTION,SITE_TYPE,SITE_STATE,CATEGORY_LOW_ID1,CATEGORY_LOW_ID2,CATEGORY_LOW_ID3,MAIN_SITE_FLAG,POINTBACK_FLAG,ALL_BANNERS_FLG,CREATED_BY,CREATED_ON,UPDATED_BY,UPDATED_ON)
VALUES (3,9,'sitePaidPublisherAgencyCommission','','',3,3,0,0,0,0,0,1,'TEST',NULL,NULL,NULL);
INSERT INTO PARTNER_SITE (SITE_NO,ACCOUNT_NO,SITE_NAME,URL,DESCRIPTION,SITE_TYPE,SITE_STATE,CATEGORY_LOW_ID1,CATEGORY_LOW_ID2,CATEGORY_LOW_ID3,MAIN_SITE_FLAG,POINTBACK_FLAG,ALL_BANNERS_FLG,CREATED_BY,CREATED_ON,UPDATED_BY,UPDATED_ON)
VALUES (1,10,'siteNotPaidDueToWithoutPublisherAgencyCommissionPolicy','','',3,3,0,0,0,0,0,1,'TEST',NULL,NULL,NULL);
INSERT INTO PARTNER_SITE (SITE_NO,ACCOUNT_NO,SITE_NAME,URL,DESCRIPTION,SITE_TYPE,SITE_STATE,CATEGORY_LOW_ID1,CATEGORY_LOW_ID2,CATEGORY_LOW_ID3,MAIN_SITE_FLAG,POINTBACK_FLAG,ALL_BANNERS_FLG,CREATED_BY,CREATED_ON,UPDATED_BY,UPDATED_ON)
VALUES (2,11,'sitePaidHalfPublisherHalfAtCommission','','',3,3,0,0,0,0,0,1,'TEST',NULL,NULL,NULL);


INSERT INTO REWARD_PRICE_HISTORY (CREATED_BY, MERCHANT_CAMPAIGN_NO, TARGET_MONTH, RANK, RESULT_ID, REWARD_TYPE, CLICK_PRICE, SALES_PRICE, SALES_RATIO, COMMISSION_TYPE, AT_COMMISSION, AT_COMMISSION_RATIO, AGENT_COMMISSION, AGENT_COMMISSION_RATIO, P_AGENT_COMMISSION, P_AGENT_COMMISSION_RATIO, REWARD_EDIT_DATE, RECORD_NUMBER, LATEST_FLAG, TARGET_DEVICE, TRANSACTION_BUDGET_CAP)
VALUES ('campaign1Rank5ResultId3BudgetCap130', 1, TO_DATE('201701', 'YYYYMM'), 5, 3, 2, 0, 0, 3, 2, 0, 1.2, 0, 1.5, 0, 1.1, NULL, 1, 1, 0, 130);
INSERT INTO REWARD_PRICE_HISTORY (CREATED_BY, MERCHANT_CAMPAIGN_NO, TARGET_MONTH, RANK, RESULT_ID, REWARD_TYPE, CLICK_PRICE, SALES_PRICE, SALES_RATIO, COMMISSION_TYPE, AT_COMMISSION, AT_COMMISSION_RATIO, AGENT_COMMISSION, AGENT_COMMISSION_RATIO, P_AGENT_COMMISSION, P_AGENT_COMMISSION_RATIO, REWARD_EDIT_DATE, RECORD_NUMBER, LATEST_FLAG, TARGET_DEVICE, TRANSACTION_BUDGET_CAP)
VALUES ('campaign5Rank5ResultId1', 5, TO_DATE('201701', 'YYYYMM'), 5, 1, 1, 0, 1.5, 0, 1, 2000, 0, 250, 0, 300, 0, NULL, 1, 1, 0, null);
INSERT INTO REWARD_PRICE_HISTORY (CREATED_BY, MERCHANT_CAMPAIGN_NO, TARGET_MONTH, RANK, RESULT_ID, REWARD_TYPE, CLICK_PRICE, SALES_PRICE, SALES_RATIO, COMMISSION_TYPE, AT_COMMISSION, AT_COMMISSION_RATIO, AGENT_COMMISSION, AGENT_COMMISSION_RATIO, P_AGENT_COMMISSION, P_AGENT_COMMISSION_RATIO, REWARD_EDIT_DATE, RECORD_NUMBER, LATEST_FLAG, TARGET_DEVICE, PRODUCT_CATEGORY_BUDGET_CAP)
VALUES ('campaign14AmountSoldBudgetCap100', 14, TO_DATE('201903', 'YYYYMM'), 5, 30, 2, 0, 0, 5, 2, 0, 3, 0, 2, 0, 1, NULL, 1, 1, 0, 100);

INSERT INTO REWARD_GOODS_HISTORY (MERCHANT_CAMPAIGN_NO,TARGET_MONTH,"RANK",GOODS_ID,REWARD_TYPE,CLICK_PRICE,SALES_PRICE,SALES_RATIO,COMMISSION_TYPE,AT_COMMISSION,AT_COMMISSION_RATIO,AGENT_COMMISSION,AGENT_COMMISSION_RATIO,P_AGENT_COMMISSION,P_AGENT_COMMISSION_RATIO,REWARD_EDIT_DATE,RECORD_NUMBER,LATEST_FLAG,TARGET_DEVICE,CREATED_BY,CREATED_ON,UPDATED_BY,UPDATED_ON)
VALUES (6,'2017-01-01 00:00:00.000',5,'goodsId8',2,0.00,0.00,7.00,2,0.00,3.00,0.00,1.00,0.00,2.00,NULL,1,1,0,NULL,NULL,NULL,NULL);
INSERT INTO REWARD_GOODS_HISTORY (MERCHANT_CAMPAIGN_NO,TARGET_MONTH,"RANK",GOODS_ID,REWARD_TYPE,CLICK_PRICE,SALES_PRICE,SALES_RATIO,COMMISSION_TYPE,AT_COMMISSION,AT_COMMISSION_RATIO,AGENT_COMMISSION,AGENT_COMMISSION_RATIO,P_AGENT_COMMISSION,P_AGENT_COMMISSION_RATIO,REWARD_EDIT_DATE,RECORD_NUMBER,LATEST_FLAG,TARGET_DEVICE,CREATED_BY,CREATED_ON,UPDATED_BY,UPDATED_ON)
VALUES (6,'2017-01-01 00:00:00.000',5,'goodsId10',2,0.00,0.00,7.00,2,0.00,3.00,0.00,1.00,0.00,1.00,NULL,1,1,0,NULL,NULL,NULL,NULL);
INSERT INTO REWARD_GOODS_HISTORY (MERCHANT_CAMPAIGN_NO,TARGET_MONTH,"RANK",GOODS_ID,REWARD_TYPE,CLICK_PRICE,SALES_PRICE,SALES_RATIO,COMMISSION_TYPE,AT_COMMISSION,AT_COMMISSION_RATIO,AGENT_COMMISSION,AGENT_COMMISSION_RATIO,P_AGENT_COMMISSION,P_AGENT_COMMISSION_RATIO,REWARD_EDIT_DATE,RECORD_NUMBER,LATEST_FLAG,TARGET_DEVICE,CREATED_BY,CREATED_ON,UPDATED_BY,UPDATED_ON)
VALUES (6,'2017-01-01 00:00:00.000',5,'goodsId9',2,0.00,7.00,0.00,1,3.00,0.00,1.00,0.00,2.00,0.00,NULL,1,1,0,NULL,NULL,NULL,NULL);
INSERT INTO REWARD_GOODS_HISTORY (MERCHANT_CAMPAIGN_NO,TARGET_MONTH,"RANK",GOODS_ID,REWARD_TYPE,CLICK_PRICE,SALES_PRICE,SALES_RATIO,COMMISSION_TYPE,AT_COMMISSION,AT_COMMISSION_RATIO,AGENT_COMMISSION,AGENT_COMMISSION_RATIO,P_AGENT_COMMISSION,P_AGENT_COMMISSION_RATIO,REWARD_EDIT_DATE,RECORD_NUMBER,LATEST_FLAG,TARGET_DEVICE,CREATED_BY,CREATED_ON,UPDATED_BY,UPDATED_ON)
VALUES (6,'2017-01-01 00:00:00.000',5,'goodsId11',2,0.00,7.00,0.00,1,3.00,0.00,1.00,0.00,1.00,0.00,NULL,1,1,0,NULL,NULL,NULL,NULL);

INSERT INTO REWARD_CATEGORY_HISTORY (CREATED_BY, CATEGORY_ID, MERCHANT_CAMPAIGN_NO, TARGET_MONTH, RANK, REWARD_TYPE, CLICK_PRICE, SALES_PRICE, SALES_RATIO, COMMISSION_TYPE, AT_COMMISSION, AT_COMMISSION_RATIO, AGENT_COMMISSION, AGENT_COMMISSION_RATIO, P_AGENT_COMMISSION, P_AGENT_COMMISSION_RATIO, RECORD_NUMBER, LATEST_FLAG, TARGET_DEVICE, BUDGET_CAP)
VALUES('campaign11FixedAmountBudgetCap1000', 'category1', 11, TO_DATE('201903', 'YYYYMM'), 5, 1, 0, 100, 0, 1, 60, 0, 40, 0, 20, 0, 1, 1, 0, 1000);
INSERT INTO REWARD_CATEGORY_HISTORY (CREATED_BY, CATEGORY_ID, MERCHANT_CAMPAIGN_NO, TARGET_MONTH, RANK, REWARD_TYPE, CLICK_PRICE, SALES_PRICE, SALES_RATIO, COMMISSION_TYPE, AT_COMMISSION, AT_COMMISSION_RATIO, AGENT_COMMISSION, AGENT_COMMISSION_RATIO, P_AGENT_COMMISSION, P_AGENT_COMMISSION_RATIO, RECORD_NUMBER, LATEST_FLAG, TARGET_DEVICE, BUDGET_CAP)
VALUES('campaign12FixedAmountBudgetCap50', 'category2', 12, TO_DATE('201903', 'YYYYMM'), 5, 1, 0, 100, 0, 1, 60, 0, 40, 0, 20, 0, 1, 1, 0, 50);
INSERT INTO REWARD_CATEGORY_HISTORY (CREATED_BY, CATEGORY_ID, MERCHANT_CAMPAIGN_NO, TARGET_MONTH, RANK, REWARD_TYPE, CLICK_PRICE, SALES_PRICE, SALES_RATIO, COMMISSION_TYPE, AT_COMMISSION, AT_COMMISSION_RATIO, AGENT_COMMISSION, AGENT_COMMISSION_RATIO, P_AGENT_COMMISSION, P_AGENT_COMMISSION_RATIO, RECORD_NUMBER, LATEST_FLAG, TARGET_DEVICE, BUDGET_CAP)
VALUES('campaign13AmountSoldBudgetCap100', 'category3', 13, TO_DATE('201903', 'YYYYMM'), 5, 2, 0, 0, 5, 2, 0, 3, 0, 2, 0, 1, 1, 1, 0, 100);

INSERT INTO CURRENCY_MASTER (CURRENCY, FRACTIONAL_DIGITS) VALUES ('IDR', 0);
INSERT INTO CURRENCY_MASTER (CURRENCY, FRACTIONAL_DIGITS) VALUES ('MYR', 2);
INSERT INTO CURRENCY_MASTER (CURRENCY, FRACTIONAL_DIGITS) VALUES ('SGD', 2);
INSERT INTO CURRENCY_MASTER (CURRENCY, FRACTIONAL_DIGITS) VALUES ('USD', 3);
INSERT INTO CURRENCY_MASTER (CURRENCY, FRACTIONAL_DIGITS) VALUES ('VND', 1);
